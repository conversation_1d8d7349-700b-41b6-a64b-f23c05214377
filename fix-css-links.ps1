# PowerShell script to update CSS links in all HTML files
Write-Host "Updating CSS links in all HTML module files..." -ForegroundColor Green

$files = @(
    "src/pages/fire-protection.html",
    "src/pages/fire.html", 
    "src/pages/introduction.html",
    "src/pages/sophos-firewalls.html",
    "src/pages/video.html"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Updating $file..." -ForegroundColor Yellow
        
        # Read file content
        $content = Get-Content $file -Raw
        
        # Replace the old CSS link with the new one
        $content = $content -replace 'href="https://cdn\.jsdelivr\.net/npm/tailwindcss@2\.2\.19/dist/tailwind\.min\.css"', 'href="../tailwind.min.css"'
        
        # Write back to file
        Set-Content -Path $file -Value $content -NoNewline
        
        Write-Host "Updated $file" -ForegroundColor Green
    } else {
        Write-Host "File $file not found" -ForegroundColor Red
    }
}

Write-Host "Done updating CSS links!" -ForegroundColor Green
