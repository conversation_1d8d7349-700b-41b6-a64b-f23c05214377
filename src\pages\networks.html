﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أساسيات الشبكات - دورة الشبكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/custom.css">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .module-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        }
        
        .interactive-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .interactive-card:hover {
            transform: translateY(-5px);
        }
        
        .osi-layer {
            transition: all 0.3s ease;
        }
        
        .osi-layer:hover {
            transform: scale(1.02);
        }
        
        .network-device {
            transition: all 0.3s ease;
        }
        
        .network-device:hover {
            transform: scale(1.1);
        }
        
        .quiz-option {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .quiz-option:hover {
            background-color: #e0e7ff;
            transform: translateX(10px);
            border-color: #3b82f6;
        }
        
        .quiz-option.correct {
            background-color: #dcfce7;
            border-color: #22c55e;
        }
        
        .quiz-option.incorrect {
            background-color: #fee2e2;
            border-color: #ef4444;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-green-50 to-teal-100 text-gray-800">

    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-reverse space-x-2">
                    <i class="fas fa-network-wired text-green-600 text-2xl"></i>
                    <span class="font-bold text-xl text-gray-800">أساسيات الشبكات</span>
                </div>
                <div class="flex space-x-reverse space-x-4">
                    <a href="/" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-home ml-1"></i>
                        الرئيسية
                    </a>
                    <button onclick="completeModule()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-check ml-1"></i>
                        إكمال الوحدة
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header class="bg-gradient-to-r from-green-600 via-teal-600 to-blue-600 text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-network-wired text-8xl"></i>
            </div>
            <h1 class="text-5xl font-bold mb-4">أساسيات الشبكات</h1>
            <p class="text-xl">تعلم كيفية عمل الشبكات وربط الأجهزة ببعضها البعض</p>
        </div>
    </header>

    <!-- Interactive Network Topology Simulator -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-6xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-sitemap text-green-600 ml-2"></i>
                    محاكي تصميم الشبكات التفاعلي
                </h2>
                
                <!-- Network Canvas -->
                <div class="bg-gray-100 rounded-lg p-8 mb-6 relative overflow-hidden" id="network-canvas">
                    <div class="grid grid-cols-4 gap-8 h-96 relative">
                        
                        <!-- Router -->
                        <div class="network-device router col-span-2 flex items-center justify-center" 
                             data-device="router" 
                             style="grid-column: 2 / 4; grid-row: 2;">
                            <div class="bg-blue-500 text-white p-6 rounded-xl shadow-lg cursor-pointer transform transition-all duration-300">
                                <i class="fas fa-wifi text-4xl mb-2"></i>
                                <p class="text-sm font-bold">Router</p>
                                <p class="text-xs">الموجه</p>
                            </div>
                        </div>

                        <!-- Computers -->
                        <div class="network-device computer" 
                             data-device="computer1" 
                             style="grid-column: 1; grid-row: 1;">
                            <div class="bg-green-500 text-white p-4 rounded-lg shadow-lg cursor-pointer transform transition-all duration-300">
                                <i class="fas fa-desktop text-2xl mb-2"></i>
                                <p class="text-xs font-bold">PC 1</p>
                            </div>
                        </div>

                        <div class="network-device computer" 
                             data-device="computer2" 
                             style="grid-column: 4; grid-row: 1;">
                            <div class="bg-green-500 text-white p-4 rounded-lg shadow-lg cursor-pointer transform transition-all duration-300">
                                <i class="fas fa-desktop text-2xl mb-2"></i>
                                <p class="text-xs font-bold">PC 2</p>
                            </div>
                        </div>

                        <div class="network-device computer" 
                             data-device="computer3" 
                             style="grid-column: 1; grid-row: 3;">
                            <div class="bg-green-500 text-white p-4 rounded-lg shadow-lg cursor-pointer transform transition-all duration-300">
                                <i class="fas fa-laptop text-2xl mb-2"></i>
                                <p class="text-xs font-bold">Laptop</p>
                            </div>
                        </div>

                        <div class="network-device computer" 
                             data-device="computer4" 
                             style="grid-column: 4; grid-row: 3;">
                            <div class="bg-green-500 text-white p-4 rounded-lg shadow-lg cursor-pointer transform transition-all duration-300">
                                <i class="fas fa-mobile-alt text-2xl mb-2"></i>
                                <p class="text-xs font-bold">Mobile</p>
                            </div>
                        </div>

                        <!-- Connection Lines (SVG) -->
                        <svg class="absolute inset-0 w-full h-full pointer-events-none" style="z-index: 1;">
                            <line id="line1" x1="20%" y1="25%" x2="50%" y2="50%" stroke="#10b981" stroke-width="3" stroke-dasharray="5,5" opacity="0">
                                <animate attributeName="stroke-dashoffset" values="10;0" dur="1s" repeatCount="indefinite"/>
                            </line>
                            <line id="line2" x1="80%" y1="25%" x2="50%" y2="50%" stroke="#10b981" stroke-width="3" stroke-dasharray="5,5" opacity="0">
                                <animate attributeName="stroke-dashoffset" values="10;0" dur="1s" repeatCount="indefinite"/>
                            </line>
                            <line id="line3" x1="20%" y1="75%" x2="50%" y2="50%" stroke="#10b981" stroke-width="3" stroke-dasharray="5,5" opacity="0">
                                <animate attributeName="stroke-dashoffset" values="10;0" dur="1s" repeatCount="indefinite"/>
                            </line>
                            <line id="line4" x1="80%" y1="75%" x2="50%" y2="50%" stroke="#10b981" stroke-width="3" stroke-dasharray="5,5" opacity="0">
                                <animate attributeName="stroke-dashoffset" values="10;0" dur="1s" repeatCount="indefinite"/>
                            </line>
                        </svg>
                    </div>
                </div>

                <!-- Network Controls -->
                <div class="flex justify-center space-x-reverse space-x-4 mb-6">
                    <button onclick="connectDevices()" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-link ml-2"></i>
                        ربط الأجهزة
                    </button>
                    <button onclick="sendPacket()" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors" id="send-packet-btn" disabled>
                        <i class="fas fa-paper-plane ml-2"></i>
                        إرسال حزمة بيانات
                    </button>
                    <button onclick="resetNetwork()" class="bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-redo ml-2"></i>
                        إعادة تعيين
                    </button>
                </div>

                <!-- Network Status -->
                <div id="network-status" class="bg-gray-100 p-4 rounded-lg text-center">
                    <p class="text-gray-600">انقر على "ربط الأجهزة" لبدء المحاكي</p>
                </div>
            </div>
        </div>
    </section>

    <!-- OSI Model Interactive -->
    <section class="py-12 px-4 bg-gray-50">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-layer-group text-blue-600 ml-2"></i>
                    نموذج OSI التفاعلي
                </h2>

                <div class="space-y-3">
                    <!-- Layer 7: Application -->
                    <div class="osi-layer bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg cursor-pointer" onclick="showLayerInfo(7)">
                        <div class="flex justify-between items-center">
                            <span class="font-bold">7. طبقة التطبيق (Application)</span>
                            <i class="fas fa-desktop"></i>
                        </div>
                    </div>

                    <!-- Layer 6: Presentation -->
                    <div class="osi-layer bg-gradient-to-r from-indigo-500 to-indigo-600 text-white p-4 rounded-lg cursor-pointer" onclick="showLayerInfo(6)">
                        <div class="flex justify-between items-center">
                            <span class="font-bold">6. طبقة العرض (Presentation)</span>
                            <i class="fas fa-file-code"></i>
                        </div>
                    </div>

                    <!-- Layer 5: Session -->
                    <div class="osi-layer bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-lg cursor-pointer" onclick="showLayerInfo(5)">
                        <div class="flex justify-between items-center">
                            <span class="font-bold">5. طبقة الجلسة (Session)</span>
                            <i class="fas fa-handshake"></i>
                        </div>
                    </div>

                    <!-- Layer 4: Transport -->
                    <div class="osi-layer bg-gradient-to-r from-teal-500 to-teal-600 text-white p-4 rounded-lg cursor-pointer" onclick="showLayerInfo(4)">
                        <div class="flex justify-between items-center">
                            <span class="font-bold">4. طبقة النقل (Transport)</span>
                            <i class="fas fa-truck"></i>
                        </div>
                    </div>

                    <!-- Layer 3: Network -->
                    <div class="osi-layer bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg cursor-pointer" onclick="showLayerInfo(3)">
                        <div class="flex justify-between items-center">
                            <span class="font-bold">3. طبقة الشبكة (Network)</span>
                            <i class="fas fa-route"></i>
                        </div>
                    </div>

                    <!-- Layer 2: Data Link -->
                    <div class="osi-layer bg-gradient-to-r from-yellow-500 to-yellow-600 text-white p-4 rounded-lg cursor-pointer" onclick="showLayerInfo(2)">
                        <div class="flex justify-between items-center">
                            <span class="font-bold">2. طبقة ربط البيانات (Data Link)</span>
                            <i class="fas fa-link"></i>
                        </div>
                    </div>

                    <!-- Layer 1: Physical -->
                    <div class="osi-layer bg-gradient-to-r from-red-500 to-red-600 text-white p-4 rounded-lg cursor-pointer" onclick="showLayerInfo(1)">
                        <div class="flex justify-between items-center">
                            <span class="font-bold">1. الطبقة الفيزيائية (Physical)</span>
                            <i class="fas fa-ethernet"></i>
                        </div>
                    </div>
                </div>

                <!-- Layer Info Panel -->
                <div id="layer-info" class="mt-6 p-6 bg-gray-50 rounded-lg hidden">
                    <h3 id="layer-title" class="text-xl font-bold mb-3"></h3>
                    <p id="layer-description" class="text-gray-700"></p>
                </div>
            </div>
        </div>
    </section>

    <!-- IP Address Calculator -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-calculator text-purple-600 ml-2"></i>
                    حاسبة عناوين IP
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <label class="block text-lg font-semibold mb-2">عنوان IP:</label>
                        <input type="text" id="ip-input" class="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none" placeholder="***********" value="***********">
                        
                        <label class="block text-lg font-semibold mb-2 mt-4">قناع الشبكة الفرعية:</label>
                        <select id="subnet-mask" class="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none">
                            <option value="24">/24 (*************)</option>
                            <option value="16">/16 (***********)</option>
                            <option value="8">/8 (*********)</option>
                            <option value="25">/25 (***************)</option>
                            <option value="26">/26 (***************)</option>
                            <option value="27">/27 (***************)</option>
                        </select>

                        <button onclick="calculateIP()" class="w-full mt-4 bg-purple-500 text-white py-3 rounded-lg hover:bg-purple-600 transition-colors">
                            <i class="fas fa-calculator ml-2"></i>
                            احسب
                        </button>
                    </div>

                    <div id="ip-results" class="space-y-4">
                        <div class="result-item p-3 bg-gray-50 rounded-lg">
                            <span class="font-semibold">عنوان الشبكة:</span>
                            <span id="network-address" class="float-left">-</span>
                        </div>
                        <div class="result-item p-3 bg-gray-50 rounded-lg">
                            <span class="font-semibold">عنوان البث:</span>
                            <span id="broadcast-address" class="float-left">-</span>
                        </div>
                        <div class="result-item p-3 bg-gray-50 rounded-lg">
                            <span class="font-semibold">أول عنوان متاح:</span>
                            <span id="first-host" class="float-left">-</span>
                        </div>
                        <div class="result-item p-3 bg-gray-50 rounded-lg">
                            <span class="font-semibold">آخر عنوان متاح:</span>
                            <span id="last-host" class="float-left">-</span>
                        </div>
                        <div class="result-item p-3 bg-gray-50 rounded-lg">
                            <span class="font-semibold">عدد المضيفين:</span>
                            <span id="host-count" class="float-left">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Network Basics Section -->
    <section class="py-12 px-4 bg-blue-50">
        <div class="container mx-auto max-w-6xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-4xl font-bold text-center mb-8">
                    <i class="fas fa-network-wired text-blue-600 ml-2"></i>
                    أساسيات الشبكات
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Network Components -->
                    <div class="bg-gradient-to-br from-blue-500 to-blue-600 text-white p-6 rounded-xl hover:scale-105 transition-transform duration-300">
                        <h3 class="text-2xl font-bold mb-4">
                            <i class="fas fa-cubes ml-2"></i>
                            مكونات الشبكة
                        </h3>
                        <ul class="space-y-2 text-sm">
                            <li><i class="fas fa-wifi ml-2"></i> المودم (Modem)</li>
                            <li><i class="fas fa-ethernet ml-2"></i> الموجه (Router)</li>
                            <li><i class="fas fa-plug ml-2"></i> المحول (Switch)</li>
                            <li><i class="fas fa-broadcast-tower ml-2"></i> نقطة الوصول (Access Point)</li>
                            <li><i class="fas fa-cable ml-2"></i> الكابلات (Cables)</li>
                        </ul>
                    </div>
                    
                    <!-- Network Types -->
                    <div class="bg-gradient-to-br from-green-500 to-green-600 text-white p-6 rounded-xl hover:scale-105 transition-transform duration-300">
                        <h3 class="text-2xl font-bold mb-4">
                            <i class="fas fa-sitemap ml-2"></i>
                            أنواع الشبكات
                        </h3>
                        <ul class="space-y-2 text-sm">
                            <li><i class="fas fa-home ml-2"></i> شبكة محلية (LAN)</li>
                            <li><i class="fas fa-building ml-2"></i> شبكة واسعة (WAN)</li>
                            <li><i class="fas fa-city ml-2"></i> شبكة حضرية (MAN)</li>
                            <li><i class="fas fa-users ml-2"></i> شبكة شخصية (PAN)</li>
                            <li><i class="fas fa-cloud ml-2"></i> الشبكة السحابية</li>
                        </ul>
                    </div>
                    
                    <!-- Network Protocols -->
                    <div class="bg-gradient-to-br from-purple-500 to-purple-600 text-white p-6 rounded-xl hover:scale-105 transition-transform duration-300">
                        <h3 class="text-2xl font-bold mb-4">
                            <i class="fas fa-file-code ml-2"></i>
                            بروتوكولات الشبكة
                        </h3>
                        <ul class="space-y-2 text-sm">
                            <li><i class="fas fa-globe ml-2"></i> HTTP/HTTPS</li>
                            <li><i class="fas fa-envelope ml-2"></i> SMTP/POP3</li>
                            <li><i class="fas fa-folder ml-2"></i> FTP/SFTP</li>
                            <li><i class="fas fa-terminal ml-2"></i> SSH/Telnet</li>
                            <li><i class="fas fa-server ml-2"></i> DNS/DHCP</li>
                        </ul>
                    </div>
                </div>
                
                <!-- IP Address Explanation -->
                <div class="mt-8 bg-gray-50 rounded-xl p-6">
                    <h3 class="text-2xl font-bold mb-4">
                        <i class="fas fa-address-card text-blue-600 ml-2"></i>
                        فهم عناوين IP
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-bold text-lg mb-2">IPv4</h4>
                            <p class="text-gray-700 mb-2">عنوان مكون من 4 أجزاء مفصولة بنقاط</p>
                            <div class="bg-blue-100 p-3 rounded-lg font-mono text-sm">
                                ************
                            </div>
                            <p class="text-sm text-gray-600 mt-2">مثال: عنوان IP خاص للشبكة المحلية</p>
                        </div>
                        <div>
                            <h4 class="font-bold text-lg mb-2">IPv6</h4>
                            <p class="text-gray-700 mb-2">عنوان أطول يحل مشكلة نفاد عناوين IPv4</p>
                            <div class="bg-green-100 p-3 rounded-lg font-mono text-sm">
                                2001:db8::1
                            </div>
                            <p class="text-sm text-gray-600 mt-2">مثال: عنوان IPv6 مبسط</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Troubleshooting Tools -->
    <section class="py-12 px-4 bg-gray-50">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-tools text-gray-600 ml-2"></i>
                    أدوات استكشاف الأخطاء
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <button onclick="simulateIpconfig()" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-all transform hover:scale-105">
                        <i class="fas fa-network-wired ml-2"></i>
                        تشغيل ipconfig
                    </button>
                    <button onclick="simulatePing()" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-all transform hover:scale-105">
                        <i class="fas fa-satellite-dish ml-2"></i>
                        تشغيل ping
                    </button>
                    <button onclick="simulateTracert()" class="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-all transform hover:scale-105">
                        <i class="fas fa-route ml-2"></i>
                        تشغيل tracert
                    </button>
                </div>

                <div class="bg-black text-green-400 p-6 rounded-lg font-mono text-sm overflow-auto" style="height: 300px;">
                    <div id="troubleshooting-output">
                        <span class="text-gray-400">انقر على أحد الأزرار أعلاه لمحاكاة أدوات استكشاف الأخطاء...</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Network Quiz Section -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-question-circle text-indigo-600 ml-2"></i>
                    اختبار أساسيات الشبكات
                </h2>
                
                <!-- Question 1 -->
                <div class="quiz-question mb-8 p-6 bg-gray-50 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">السؤال 1: ما هو الهدف الرئيسي من طبقة الشبكة في نموذج OSI؟</h3>
                    <div class="space-y-3">
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, false)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            إدارة الجلسات بين التطبيقات
                        </div>
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, true)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            توجيه البيانات بين الشبكات المختلفة
                        </div>
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, false)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            تشفير البيانات
                        </div>
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, false)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            النقل الفيزيائي للبيانات
                        </div>
                    </div>
                </div>

                <!-- Question 2 -->
                <div class="quiz-question mb-8 p-6 bg-gray-50 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">السؤال 2: ما هو البروتوكول المستخدم لترجمة أسماء المواقع إلى عناوين IP؟</h3>
                    <div class="space-y-3">
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, false)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            HTTP
                        </div>
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, true)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            DNS
                        </div>
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, false)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            DHCP
                        </div>
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, false)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            FTP
                        </div>
                    </div>
                </div>

                <!-- Question 3 -->
                <div class="quiz-question mb-8 p-6 bg-gray-50 rounded-lg">
                    <h3 class="text-xl font-semibold mb-4">السؤال 3: ما هو الفرق بين Switch و Hub؟</h3>
                    <div class="space-y-3">
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, false)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            لا يوجد فرق
                        </div>
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, true)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            Switch يرسل البيانات للجهاز المطلوب فقط، Hub يرسل لجميع الأجهزة
                        </div>
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, false)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            Hub أسرع من Switch
                        </div>
                        <div class="quiz-option p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer transition-colors" onclick="selectNetworkAnswer(this, false)">
                            <i class="fas fa-circle-dot text-blue-500 ml-2"></i>
                            Switch يعمل في الطبقة الفيزيائية فقط
                        </div>
                    </div>
                </div>

                <!-- Quiz Results -->
                <div id="network-quiz-results" class="hidden bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                    <h3 class="text-2xl font-bold text-green-800 mb-4">
                        <i class="fas fa-trophy ml-2"></i>
                        نتائج الاختبار
                    </h3>
                    <p class="text-lg text-green-700 mb-4">
                        لقد أجبت على <span id="network-quiz-score" class="font-bold">0</span> من 3 أسئلة بشكل صحيح
                    </p>
                    <div class="text-sm text-green-600">
                        <p id="network-quiz-message">أحسنت! لقد أتممت الاختبار بنجاح.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2025 دورة الشبكات وأمن المعلومات</p>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/networks.js"></script>
    <script>
        // Quiz functionality for network questions
        let networkQuizScore = 0;
        let networkAnsweredQuestions = 0;
        const totalNetworkQuestions = 3;
        
        function selectNetworkAnswer(element, isCorrect) {
            // Disable all options in this question
            const questionDiv = element.closest('.quiz-question');
            const allOptions = questionDiv.querySelectorAll('.quiz-option');
            
            allOptions.forEach(option => {
                option.style.pointerEvents = 'none';
                option.classList.add('opacity-60');
            });

            // Mark the selected answer
            if (isCorrect) {
                element.classList.add('correct');
                element.innerHTML += ' <i class="fas fa-check text-green-600 float-left"></i>';
                networkQuizScore++;
            } else {
                element.classList.add('incorrect');
                element.innerHTML += ' <i class="fas fa-times text-red-600 float-left"></i>';
                
                // Show correct answer
                allOptions.forEach(option => {
                    if (option.onclick && option.onclick.toString().includes('true')) {
                        option.classList.add('correct');
                        option.innerHTML += ' <i class="fas fa-check text-green-600 float-left"></i>';
                    }
                });
            }

            networkAnsweredQuestions++;
            
            // Show results if all questions answered
            if (networkAnsweredQuestions >= totalNetworkQuestions) {
                setTimeout(() => {
                    showNetworkQuizResults();
                }, 1000);
            }
        }

        function showNetworkQuizResults() {
            const resultsDiv = document.getElementById('network-quiz-results');
            const scoreSpan = document.getElementById('network-quiz-score');
            const messageEl = document.getElementById('network-quiz-message');
            
            scoreSpan.textContent = networkQuizScore;
            
            if (networkQuizScore >= 2) {
                messageEl.textContent = 'ممتاز! لقد أتممت الاختبار بنجاح.';
            } else {
                messageEl.textContent = 'يمكنك المحاولة مرة أخرى لتحسين نتيجتك.';
            }
            
            resultsDiv.classList.remove('hidden');
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
