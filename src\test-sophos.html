<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Sophos Firewalls Module</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">اختبار وحدة Sophos Firewalls</h1>
        
        <!-- Test Buttons -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-4">اختبار الوظائف المحسنة</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="testAttackSimulation()" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600">
                    <i class="fas fa-virus ml-2"></i>
                    اختبار محاكاة الهجمات
                </button>
                <button onclick="testNetworkScan()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                    <i class="fas fa-search ml-2"></i>
                    اختبار فحص الشبكة
                </button>
                <button onclick="testQuizSystem()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                    <i class="fas fa-question-circle ml-2"></i>
                    اختبار نظام الاختبار
                </button>
                <button onclick="testReporting()" class="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600">
                    <i class="fas fa-chart-bar ml-2"></i>
                    اختبار التقارير
                </button>
            </div>
        </div>

        <!-- Test Results -->
        <div id="test-results" class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold mb-4">نتائج الاختبار</h2>
            <div id="test-output" class="space-y-3">
                <p class="text-gray-600">اضغط على الأزرار أعلاه لاختبار الوظائف...</p>
            </div>
        </div>

        <!-- Mock Elements for Testing -->
        <div class="hidden">
            <div id="active-protections">0</div>
            <div id="detected-threats">0</div>
            <div id="network-status">Offline</div>
            <div id="blocked-attacks">0</div>
            <div id="detected-viruses">0</div>
            <div id="safe-connections">0</div>
            <div id="security-alerts"></div>
            <div id="event-log"></div>
            <div id="scan-button"></div>
            <div id="scan-progress" class="hidden"></div>
            <div id="scan-results" class="hidden"></div>
            <div id="progress-bar"></div>
            <div id="scan-status"></div>
            <div id="scan-results-content"></div>
            <div id="quiz-container"></div>
            <div id="question-title"></div>
            <div id="answers-container"></div>
            <div id="question-counter"></div>
            <div id="quiz-results" class="hidden"></div>
            <div id="score-display"></div>
            <div id="score-message"></div>
            <button id="next-button" disabled></button>
            <button id="prev-button" disabled></button>
        </div>
    </div>

    <script src="assets/js/sophos-firewalls.js"></script>
    <script>
        // Test Functions
        function testAttackSimulation() {
            addTestResult('اختبار محاكاة الهجمات...', 'info');
            firewallActive = true; // Enable firewall for testing
            
            // Test different attack types
            const attackTypes = ['malware', 'ddos', 'intrusion', 'phishing', 'ransomware'];
            
            attackTypes.forEach((type, index) => {
                setTimeout(() => {
                    simulateAttack(type);
                    addTestResult(`تم اختبار هجوم ${type}`, 'success');
                }, index * 1000);
            });
        }

        function testNetworkScan() {
            addTestResult('اختبار فحص الشبكة...', 'info');
            
            // Mock elements
            const scanButton = document.getElementById('scan-button');
            const scanProgress = document.getElementById('scan-progress');
            const scanResults = document.getElementById('scan-results');
            const progressBar = document.getElementById('progress-bar');
            const scanStatus = document.getElementById('scan-status');
            const scanResultsContent = document.getElementById('scan-results-content');
            
            startNetworkScan();
            addTestResult('تم بدء فحص الشبكة', 'success');
        }

        function testQuizSystem() {
            addTestResult('اختبار نظام الاختبار...', 'info');
            
            // Test quiz loading
            loadQuiz();
            
            // Test question navigation
            setTimeout(() => {
                nextQuestion();
                addTestResult('تم الانتقال للسؤال التالي', 'success');
            }, 500);
            
            setTimeout(() => {
                previousQuestion();
                addTestResult('تم الرجوع للسؤال السابق', 'success');
            }, 1000);
        }

        function testReporting() {
            addTestResult('اختبار التقارير...', 'info');
            
            // Test report generation
            generateReport();
            
            // Test real-time dashboard updates
            updateRealTimeDashboard();
            updateStatistics();
            
            addTestResult('تم إنشاء التقرير وتحديث الإحصائيات', 'success');
        }

        function addTestResult(message, type) {
            const output = document.getElementById('test-output');
            const resultDiv = document.createElement('div');
            
            let bgColor = 'bg-blue-100 border-blue-200 text-blue-800';
            let icon = 'info-circle';
            
            if (type === 'success') {
                bgColor = 'bg-green-100 border-green-200 text-green-800';
                icon = 'check-circle';
            } else if (type === 'error') {
                bgColor = 'bg-red-100 border-red-200 text-red-800';
                icon = 'exclamation-circle';
            }
            
            resultDiv.className = `p-3 rounded-lg border ${bgColor}`;
            resultDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${icon} ml-2"></i>
                    <span>${message}</span>
                    <span class="text-xs opacity-75 mr-auto">${new Date().toLocaleTimeString('ar-SA')}</span>
                </div>
            `;
            
            output.appendChild(resultDiv);
            output.scrollTop = output.scrollHeight;
        }

        // Initialize test
        window.addEventListener('DOMContentLoaded', function() {
            addTestResult('تم تحميل صفحة الاختبار بنجاح', 'success');
        });
    </script>
</body>
</html>
