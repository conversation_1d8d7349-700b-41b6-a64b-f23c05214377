// Sophos Firewalls - Enhanced Version

// Global State Management
let firewallActive = false;
let sophosQuizScore = 0;
let sophosAnsweredQuestions = 0;
let currentQuestionIndex = 0;
let userAnswers = [];
const sophosTotalQuestions = 5;

// Enhanced Statistics
let firewallStats = {
    blockedAttacks: 0,
    detectedViruses: 0,
    safeConnections: 0,
    activeProtections: 0,
    detectedThreats: 0,
    networkUptime: 0,
    totalTraffic: 0
};

// Quiz Questions Database
const quizQuestions = [
    {
        question: "ما هو الهدف الرئيسي من جدار الحماية Sophos؟",
        answers: [
            { text: "حماية الشبكة من التهديدات الخارجية والداخلية", correct: true },
            { text: "زيادة سرعة الإنترنت", correct: false },
            { text: "إدارة قواعد البيانات", correct: false },
            { text: "تحسين أداء الخادم", correct: false }
        ]
    },
    {
        question: "أي من هذه الميزات يوفرها Sophos Firewall؟",
        answers: [
            { text: "حماية الويب فقط", correct: false },
            { text: "مكافحة الفيروسات فقط", correct: false },
            { text: "حماية شاملة تشمل الويب والبريد الإلكتروني ومكافحة التطفل", correct: true },
            { text: "إدارة الملفات", correct: false }
        ]
    },
    {
        question: "ما هي فائدة VPN في Sophos Firewall؟",
        answers: [
            { text: "تسريع الاتصال", correct: false },
            { text: "إنشاء اتصال آمن ومشفر بين المواقع المختلفة", correct: true },
            { text: "تخزين الملفات", correct: false },
            { text: "إدارة المستخدمين", correct: false }
        ]
    },
    {
        question: "كيف يمكن لـ Sophos Firewall اكتشاف التهديدات؟",
        answers: [
            { text: "من خلال مراقبة حركة المرور وتحليل الأنماط المشبوهة", correct: true },
            { text: "من خلال قراءة رسائل البريد الإلكتروني", correct: false },
            { text: "من خلال مراقبة الطقس", correct: false },
            { text: "من خلال تتبع المواقع الاجتماعية", correct: false }
        ]
    },
    {
        question: "ما هي أهمية تحديث قواعد الحماية في Sophos Firewall؟",
        answers: [
            { text: "لتوفير المساحة", correct: false },
            { text: "لتحسين الشكل الجمالي", correct: false },
            { text: "للحماية من التهديدات الجديدة والمتطورة", correct: true },
            { text: "لتسريع الإنترنت", correct: false }
        ]
    }
];

// Attack Simulation Data
const attackTypes = {
    malware: {
        name: "هجوم فيروسي",
        description: "محاولة تسلل برمجيات خبيثة",
        severity: "high",
        duration: 3000
    },
    ddos: {
        name: "هجوم DDoS",
        description: "محاولة إغراق الشبكة بالطلبات",
        severity: "critical",
        duration: 5000
    },
    intrusion: {
        name: "محاولة اختراق",
        description: "محاولة وصول غير مصرح به",
        severity: "medium",
        duration: 2000
    },
    phishing: {
        name: "هجوم تصيد",
        description: "محاولة خداع المستخدمين",
        severity: "medium",
        duration: 2500
    },
    ransomware: {
        name: "برمجيات الفدية",
        description: "محاولة تشفير الملفات",
        severity: "critical",
        duration: 4000
    }
};

// Feature Detail Functions
function showSophosInfo(feature) {
    const infoPanel = document.getElementById('sophos-info-panel');
    const title = document.getElementById('sophos-info-title');
    const content = document.getElementById('sophos-info-content');

    const sophosData = {
        'web': { title: 'حماية الويب', content: 'حجب وعزل المواقع الضارة، وتطبيق سياسات التصفية على الويب لضمان تجربة تصفح آمنة.' },
        'ips': { title: 'منع التطفل', content: 'يكشف ويمنع التهديدات والهجمات الضارة عن طريق استخدام تقنيات منع التطفل المتقدمة.' },
        'email': { title: 'أمان البريد الإلكتروني', content: 'يوفر حماية من الرسائل غير المرغوبة والضارة مع خيارات لفحص وتشفير المرفقات والبريد.' },
        'app': { title: 'تحكم في التطبيقات', content: 'تمكين التحكم في التطبيقات بحيث يمكن تقييد الوصول والتأكد من الاستخدام الآمن للتطبيقات.' }
    };

    title.textContent = sophosData[feature].title;
    content.textContent = sophosData[feature].content;
    infoPanel.classList.remove('hidden');
    infoPanel.scrollIntoView({ behavior: 'smooth' });
}

// Sophos Configuration Control
function toggleFirewall() {
    firewallActive = !firewallActive;
    const toggleBtn = document.getElementById('firewall-toggle');
    const statusDiv = document.getElementById('firewall-status');
    const scanBtn = document.getElementById('scan-btn');

    if (firewallActive) {
        toggleBtn.innerHTML = '<i class="fas fa-power-off ml-2"></i> إيقاف الجدار النار';
        toggleBtn.classList.remove('bg-red-500', 'hover:bg-red-600');
        toggleBtn.classList.add('bg-green-500', 'hover:bg-green-600');

        scanBtn.disabled = false;
        scanBtn.classList.remove('opacity-50');

        statusDiv.className = 'bg-green-100 border border-green-200 p-4 rounded-lg text-center';
        statusDiv.innerHTML = '<p class="text-green-700">الجدار النار نشط</p>';
        addSecurityAlert('تم تفعيل جدار الحماية بنجاح', 'success');
    } else {
        toggleBtn.innerHTML = '<i class="fas fa-power-off ml-2"></i> تشغيل الجدار النار';
        toggleBtn.classList.remove('bg-green-500', 'hover:bg-green-600');
        toggleBtn.classList.add('bg-red-500', 'hover:bg-red-600');

        scanBtn.disabled = true;
        scanBtn.classList.add('opacity-50');

        statusDiv.className = 'bg-red-100 border border-red-200 p-4 rounded-lg text-center';
        statusDiv.innerHTML = '<p class="text-red-700">الجدار النار متوقف</p>';
        addSecurityAlert('تم إيقاف جدار الحماية', 'info');
    }
}

function performScan() {
    if (!firewallActive) return;

    const scanResults = document.getElementById('scan-results');
    scanResults.innerHTML = '<p class="text-yellow-700 animate-pulse">جارٍ الفحص...</p>';

    setTimeout(() => {
        scanResults.innerHTML = '<p class="text-green-600">تم الفحص بنجاح - لا توجد تهديدات</p>';
        addSecurityAlert('فحص الشبكة النظيف - كل شيء آمن', 'success');
    }, 2000);
}

function viewReports() {
    addSecurityAlert('عرض تقارير الأمان - افحص التنبيهات والسجلات بانتظام', 'info');
}

function togglePolicy(policy) {
    const policyBtn = document.getElementById(`${policy}-policy`);
    const policies = document.getElementById('active-policies');

    if (policyBtn.classList.contains('bg-gray-300')) {
        policyBtn.classList.remove('bg-gray-300');
        policyBtn.classList.add('bg-green-300', 'text-green-700');
        policyBtn.textContent = 'نشط';

        policies.textContent = parseInt(policies.textContent) + 1;
    } else {
        policyBtn.classList.remove('bg-green-300', 'text-green-700');
        policyBtn.classList.add('bg-gray-300');
        policyBtn.textContent = 'متوقف';

        policies.textContent = parseInt(policies.textContent) - 1;
    }
}

function addSecurityAlert(message, type = 'info') {
    logEvent(message, type);
    const alertLog = document.getElementById('security-alerts');
const timestamp = new Date().toLocaleTimeString();

    // Remove placeholder if exists
    const placeholder = alertLog.querySelector('.text-gray-500');
    if (placeholder) {
        placeholder.remove();
    }

    const alertDiv = document.createElement('div');
    alertDiv.className = `p-3 rounded-lg text-sm animate-fade-in ${getAlertStyle(type)}`;
    alertDiv.innerHTML = `
        <div class="flex justify-between items-start">
            <span>${message}</span>
            <span class="text-xs opacity-75">${timestamp}</span>
        </div>
    `;

    // Add to top of log
    alertLog.insertBefore(alertDiv, alertLog.firstChild);

    // Keep only last 10 alerts
    while (alertLog.children.length > 10) {
        alertLog.removeChild(alertLog.lastChild);
    }

    // Scroll to top
    alertLog.scrollTop = 0;
}

function getAlertStyle(type) {
    const styles = {
        success: 'bg-green-100 border border-green-200 text-green-800',
        warning: 'bg-yellow-100 border border-yellow-200 text-yellow-800',
        danger: 'bg-red-100 border border-red-200 text-red-800',
        info: 'bg-blue-100 border border-blue-200 text-blue-800'
    };
    return styles[type] || styles.info;
}

// Quiz Functions
function selectSophosAnswer(element, isCorrect) {
    const questionDiv = element.closest('.quiz-question');
    const allOptions = questionDiv.querySelectorAll('.quiz-option');

    allOptions.forEach(option => {
        option.style.pointerEvents = 'none';
        option.classList.add('opacity-60');
    });

    if (isCorrect) {
        element.classList.add('border-green-500', 'bg-green-100');
        sophosQuizScore++;
    } else {
        element.classList.add('border-red-500', 'bg-red-100');
    }

    sophosAnsweredQuestions++;
    if (sophosAnsweredQuestions >= sophosTotalQuestions) {
        showSophosQuizResults();
    }
}

// Improved VPN Simulation Functions
function vpnAdvancedSettings() {
    // Display advanced settings simulation
    const settingsPanel = document.getElementById('vpn-settings-panel');
    settingsPanel.classList.remove('hidden');
}

function vpnTroubleshoot() {
    // Simulate a troubleshooting routine
    if (!vpnConnected) {
        addSecurityAlert('لا يوجد اتصال VPN ليتم فحصه', 'warning');
        return;
    }
    const troubleshootBtn = document.getElementById('vpn-troubleshoot-btn');
    troubleshootBtn.disabled = true;
    troubleshootBtn.innerHTML = '\u003ci class="fas fa-spinner fa-spin ml-2"\u003e\u003c/i\u003e جارٍ الفحص...';

    setTimeout(() => {
        troubleshootBtn.innerHTML = '\u003ci class="fas fa-toolbox ml-2"\u003e\u003c/i\u003e فحص الأعطال';
        troubleshootBtn.disabled = false;
        addSecurityAlert('تم إكمال فحص الأعطال - كل شيء يعمل بشكل سليم', 'success');
    }, 2000);
}

function showSophosQuizResults() {
    document.getElementById('sophos-quiz-score').textContent = sophosQuizScore;
    document.getElementById('sophos-quiz-results').classList.remove('hidden');
    document.getElementById('sophos-quiz-results').scrollIntoView({ behavior: 'smooth' });
}

function completeModule() {
    let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
    progress.sophos = true;
    localStorage.setItem('courseProgress', JSON.stringify(progress));
    showNotification('تم إكمال وحدة Sophos Firewalls بنجاح!', 'success');
    setTimeout(() => {
        window.location.href = '../index.html';
    }, 1500);
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;

    if (type === 'success') {
        notification.classList.add('bg-green-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-check-circle ml-2"></i>${message}`;
    } else if (type === 'error') {
        notification.classList.add('bg-red-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-exclamation-circle ml-2"></i>${message}`;
    } else {
        notification.classList.add('bg-blue-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-info-circle ml-2"></i>${message}`;
    }

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Animate out
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// VPN Lab Functions
let vpnConnected = false;
let vpnStartTime = null;
let vpnStatsInterval = null;
let vpnPacketsSent = 0;
let vpnPacketsReceived = 0;

function establishVPN() {
    if (vpnConnected) return;
    
    // Update button states
    const establishBtn = document.getElementById('vpn-establish-btn');
    const testBtn = document.getElementById('vpn-test-btn');
    const disconnectBtn = document.getElementById('vpn-disconnect-btn');
    
    establishBtn.disabled = true;
    establishBtn.classList.add('opacity-50');
    establishBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i> جارٍ الاتصال...';
    
    // Simulate connection process
    setTimeout(() => {
        vpnConnected = true;
        vpnStartTime = new Date();
        
        // Update site statuses
        updateSiteStatus('siteA-status', 'متصل', 'success');
        updateSiteStatus('siteB-status', 'متصل', 'success');
        
        // Update connection arrow
        const arrow = document.getElementById('vpn-connection-arrow');
        arrow.className = 'text-4xl text-green-500 animate-pulse';
        arrow.innerHTML = '<i class="fas fa-exchange-alt"></i>';
        
        // Update button states
        establishBtn.innerHTML = '<i class="fas fa-check ml-2"></i> الاتصال نشط';
        testBtn.disabled = false;
        testBtn.classList.remove('opacity-50');
        disconnectBtn.disabled = false;
        disconnectBtn.classList.remove('opacity-50');
        
        // Start VPN statistics
        startVPNStats();
        
        addSecurityAlert('تم إنشاء اتصال VPN بنجاح بين الموقعين', 'success');
    }, 3000);
}

function testVPNConnection() {
    if (!vpnConnected) return;
    
    const testBtn = document.getElementById('vpn-test-btn');
    testBtn.disabled = true;
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i> جارٍ الاختبار...';
    
    // Simulate ping test
    setTimeout(() => {
        const pingResults = [
            'ping 192.168.2.1 من 192.168.1.1 - نجح (23ms)',
            'ping 192.168.1.1 من 192.168.2.1 - نجح (25ms)',
            'تم تبديل البيانات بنجاح عبر النفق الآمن'
        ];
        
        pingResults.forEach((result, index) => {
            setTimeout(() => {
                addSecurityAlert(result, 'info');
            }, index * 1000);
        });
        
        testBtn.disabled = false;
        testBtn.innerHTML = '<i class="fas fa-vial ml-2"></i> اختبار الاتصال';
        
        // Increase packet counters
        vpnPacketsSent += Math.floor(Math.random() * 50) + 10;
        vpnPacketsReceived += Math.floor(Math.random() * 45) + 8;
    }, 2000);
}

function disconnectVPN() {
    if (!vpnConnected) return;
    
    vpnConnected = false;
    
    // Update button states
    const establishBtn = document.getElementById('vpn-establish-btn');
    const testBtn = document.getElementById('vpn-test-btn');
    const disconnectBtn = document.getElementById('vpn-disconnect-btn');
    
    establishBtn.disabled = false;
    establishBtn.classList.remove('opacity-50');
    establishBtn.innerHTML = '<i class="fas fa-link ml-2"></i> إنشاء اتصال VPN';
    
    testBtn.disabled = true;
    testBtn.classList.add('opacity-50');
    disconnectBtn.disabled = true;
    disconnectBtn.classList.add('opacity-50');
    
    // Update site statuses
    updateSiteStatus('siteA-status', 'غير متصل', 'inactive');
    updateSiteStatus('siteB-status', 'غير متصل', 'inactive');
    
    // Update connection arrow
    const arrow = document.getElementById('vpn-connection-arrow');
    arrow.className = 'text-4xl text-gray-400 animate-pulse';
    arrow.innerHTML = '<i class="fas fa-arrows-alt-h"></i>';
    
    // Stop VPN statistics
    stopVPNStats();
    
    addSecurityAlert('تم قطع اتصال VPN', 'warning');
}

function updateSiteStatus(elementId, status, type) {
    const element = document.getElementById(elementId);
    let bgColor = 'bg-gray-100 border-gray-200';
    let textColor = 'text-gray-600';
    
    if (type === 'success') {
        bgColor = 'bg-green-100 border-green-200';
        textColor = 'text-green-700';
    } else if (type === 'warning') {
        bgColor = 'bg-yellow-100 border-yellow-200';
        textColor = 'text-yellow-700';
    }
    
    element.className = `p-3 ${bgColor} border rounded-lg`;
    element.innerHTML = `<span class="${textColor}">${status}</span>`;
}

function startVPNStats() {
    vpnStatsInterval = setInterval(() => {
        if (!vpnConnected) return;
        
        // Update uptime
        const uptime = Math.floor((new Date() - vpnStartTime) / 1000);
        const minutes = Math.floor(uptime / 60);
        const seconds = uptime % 60;
        document.getElementById('vpn-uptime').textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        // Update packet counters
        document.getElementById('vpn-packets-sent').textContent = vpnPacketsSent;
        document.getElementById('vpn-packets-received').textContent = vpnPacketsReceived;
        
        // Update throughput (simulated)
        const throughput = Math.floor(Math.random() * 1000) + 100;
        document.getElementById('vpn-throughput').textContent = `${throughput} KB/s`;
        
        // Randomly add packets
        if (Math.random() < 0.3) {
            vpnPacketsSent += Math.floor(Math.random() * 10) + 1;
            vpnPacketsReceived += Math.floor(Math.random() * 8) + 1;
        }
    }, 1000);
}

function stopVPNStats() {
    if (vpnStatsInterval) {
        clearInterval(vpnStatsInterval);
        vpnStatsInterval = null;
    }
    
    // Reset stats
    document.getElementById('vpn-uptime').textContent = '0:00';
    document.getElementById('vpn-packets-sent').textContent = '0';
    document.getElementById('vpn-packets-received').textContent = '0';
    document.getElementById('vpn-throughput').textContent = '0 KB/s';
    
    vpnPacketsSent = 0;
    vpnPacketsReceived = 0;
}

// Real-time Monitoring Dashboard
function updateRealTimeDashboard() {
    const activeProtectionsDisplay = document.getElementById('active-protections');
    const detectedThreatsDisplay = document.getElementById('detected-threats');
    const networkStatusDisplay = document.getElementById('network-status');
    
    activeProtectionsDisplay.textContent = firewallStats.activeProtections;
    detectedThreatsDisplay.textContent = firewallStats.detectedThreats;
    networkStatusDisplay.textContent = (firewallActive ? 'Online' : 'Offline');
}

function logEvent(description, severity = 'info') {
    const eventLog = document.getElementById('event-log');
const timestamp = new Date().toLocaleString();

    const eventEntry = document.createElement('div');
    eventEntry.className = `bg-white p-2 rounded my-2 shadow border left-2 border-${severity}`;
    eventEntry.innerHTML = `<span class="font-mono">[${timestamp}]</span> ${description}`;

    eventLog.appendChild(eventEntry);
    eventLog.scrollTop = eventLog.scrollHeight;
}

function generateReport() {
    const reportContent = `
        Firewall Active: ${firewallActive}
        Blocked Attacks: ${firewallStats.blockedAttacks}
        Detected Viruses: ${firewallStats.detectedViruses}
        Safe Connections: ${firewallStats.safeConnections}
    `;
    console.log('--- Security Report ---\n' + reportContent);
}

// Advanced Attack Simulation
function simulateAttack(attackType) {
    if (!firewallActive) {
        showNotification('يجب تشغيل الجدار الناري أولاً!', 'error');
        return;
    }

    const attack = attackTypes[attackType];
    if (!attack) return;

    addSecurityAlert(`تم اكتشاف ${attack.name}: ${attack.description}`, 'danger');
    
    // Simulate attack processing
    setTimeout(() => {
        firewallStats.detectedThreats++;
        
        if (attack.severity === 'critical') {
            firewallStats.blockedAttacks++;
            addSecurityAlert(`تم حجب ${attack.name} بنجاح`, 'success');
        } else {
            firewallStats.safeConnections++;
            addSecurityAlert(`تم التعامل مع ${attack.name} بنجاح`, 'success');
        }
        
        updateRealTimeDashboard();
        updateStatistics();
    }, attack.duration);
}

// Network Scanner
function startNetworkScan() {
    const scanButton = document.getElementById('scan-button');
    const scanProgress = document.getElementById('scan-progress');
    const scanResults = document.getElementById('scan-results');
    const progressBar = document.getElementById('progress-bar');
    const scanStatus = document.getElementById('scan-status');
    
    scanButton.disabled = true;
    scanProgress.classList.remove('hidden');
    scanResults.classList.add('hidden');
    
    let progress = 0;
    const scanSteps = [
        'فحص الشبكة المحلية...',
        'فحص البروتوكولات...',
        'فحص الأجهزة المتصلة...',
        'فحص التهديدات...',
        'إنشاء التقرير...'
    ];
    
    const interval = setInterval(() => {
        progress += 20;
        progressBar.style.width = progress + '%';
        scanStatus.textContent = scanSteps[Math.floor(progress / 20) - 1] || 'اكتمل الفحص';
        
        if (progress >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                scanProgress.classList.add('hidden');
                showScanResults();
                scanButton.disabled = false;
            }, 1000);
        }
    }, 1000);
}

function showScanResults() {
    const scanResults = document.getElementById('scan-results');
    const scanResultsContent = document.getElementById('scan-results-content');
    
    const results = [
        { type: 'success', message: 'تم العثور على 15 جهاز متصل' },
        { type: 'info', message: 'جميع البروتوكولات آمنة' },
        { type: 'warning', message: 'تم العثور على 2 اتصال مشبوه' },
        { type: 'success', message: 'تم حجب 5 محاولات اختراق' }
    ];
    
    scanResultsContent.innerHTML = '';
    
    results.forEach(result => {
        const resultDiv = document.createElement('div');
        resultDiv.className = `p-3 rounded-lg ${getAlertStyle(result.type)}`;
        resultDiv.innerHTML = `<i class="fas fa-${getIconForType(result.type)} ml-2"></i>${result.message}`;
        scanResultsContent.appendChild(resultDiv);
    });
    
    scanResults.classList.remove('hidden');
    addSecurityAlert('تم إكمال فحص الشبكة', 'info');
}

function getIconForType(type) {
    const icons = {
        success: 'check-circle',
        info: 'info-circle',
        warning: 'exclamation-triangle',
        danger: 'times-circle'
    };
    return icons[type] || 'info-circle';
}

// Configuration Management
function updateRules() {
    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i> جارٍ التحديث...';
    
    setTimeout(() => {
        button.innerHTML = '<i class="fas fa-sync ml-2"></i> تحديث القواعد';
        button.disabled = false;
        addSecurityAlert('تم تحديث قواعد الحماية بنجاح', 'success');
        firewallStats.activeProtections++;
        updateRealTimeDashboard();
    }, 2000);
}

function backupConfig() {
    const button = event.target;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i> جارٍ الحفظ...';
    
    setTimeout(() => {
        button.innerHTML = '<i class="fas fa-save ml-2"></i> حفظ الإعدادات';
        button.disabled = false;
        addSecurityAlert('تم حفظ الإعدادات بنجاح', 'success');
    }, 1500);
}

function resetFirewall() {
    if (confirm('هل أنت متأكد من إعادة تعيين الجدار الناري؟')) {
        firewallActive = false;
        firewallStats = {
            blockedAttacks: 0,
            detectedViruses: 0,
            safeConnections: 0,
            activeProtections: 0,
            detectedThreats: 0,
            networkUptime: 0,
            totalTraffic: 0
        };
        
        updateRealTimeDashboard();
        updateStatistics();
        addSecurityAlert('تم إعادة تعيين الجدار الناري', 'info');
    }
}

// Enhanced Quiz System
function loadQuiz() {
    const quizContainer = document.getElementById('quiz-container');
    const questionTitle = document.getElementById('question-title');
    const answersContainer = document.getElementById('answers-container');
    const questionCounter = document.getElementById('question-counter');
    
    if (currentQuestionIndex >= quizQuestions.length) {
        showQuizResults();
        return;
    }
    
    const question = quizQuestions[currentQuestionIndex];
    questionTitle.textContent = question.question;
    questionCounter.textContent = `السؤال ${currentQuestionIndex + 1} من ${quizQuestions.length}`;
    
    answersContainer.innerHTML = '';
    
    question.answers.forEach((answer, index) => {
        const answerDiv = document.createElement('div');
        answerDiv.className = 'p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors';
        answerDiv.innerHTML = answer.text;
        answerDiv.onclick = () => selectAnswer(index, answer.correct);
        answersContainer.appendChild(answerDiv);
    });
}

function selectAnswer(index, isCorrect) {
    const answersContainer = document.getElementById('answers-container');
    const answers = answersContainer.children;
    
    // Disable all answers
    for (let answer of answers) {
        answer.style.pointerEvents = 'none';
        answer.classList.add('opacity-60');
    }
    
    // Highlight selected answer
    if (isCorrect) {
        answers[index].classList.add('bg-green-100', 'border-green-500');
        sophosQuizScore++;
    } else {
        answers[index].classList.add('bg-red-100', 'border-red-500');
        // Show correct answer
        const correctIndex = quizQuestions[currentQuestionIndex].answers.findIndex(a => a.correct);
        answers[correctIndex].classList.add('bg-green-100', 'border-green-500');
    }
    
    userAnswers[currentQuestionIndex] = { selected: index, correct: isCorrect };
    
    // Enable next button
    document.getElementById('next-button').disabled = false;
}

function nextQuestion() {
    currentQuestionIndex++;
    loadQuiz();
    
    // Update navigation buttons
    document.getElementById('prev-button').disabled = currentQuestionIndex === 0;
    document.getElementById('next-button').disabled = true;
}

function previousQuestion() {
    if (currentQuestionIndex > 0) {
        currentQuestionIndex--;
        loadQuiz();
    }
    
    // Update navigation buttons
    document.getElementById('prev-button').disabled = currentQuestionIndex === 0;
    document.getElementById('next-button').disabled = false;
}

function showQuizResults() {
    const quizContainer = document.getElementById('quiz-container');
    const quizResults = document.getElementById('quiz-results');
    const scoreDisplay = document.getElementById('score-display');
    const scoreMessage = document.getElementById('score-message');
    
    quizContainer.classList.add('hidden');
    quizResults.classList.remove('hidden');
    
    scoreDisplay.textContent = `${sophosQuizScore}/${quizQuestions.length}`;
    
    const percentage = (sophosQuizScore / quizQuestions.length) * 100;
    if (percentage >= 80) {
        scoreMessage.textContent = 'ممتاز! لديك فهم جيد لجدران حماية Sophos';
        scoreMessage.className = 'text-green-600 mb-6';
    } else if (percentage >= 60) {
        scoreMessage.textContent = 'جيد! يمكنك مراجعة بعض المفاهيم';
        scoreMessage.className = 'text-yellow-600 mb-6';
    } else {
        scoreMessage.textContent = 'يحتاج لمزيد من الدراسة';
        scoreMessage.className = 'text-red-600 mb-6';
    }
}

function restartQuiz() {
    currentQuestionIndex = 0;
    sophosQuizScore = 0;
    userAnswers = [];
    
    document.getElementById('quiz-container').classList.remove('hidden');
    document.getElementById('quiz-results').classList.add('hidden');
    
    loadQuiz();
}

// Statistics Update
function updateStatistics() {
    const blockedAttacksDisplay = document.getElementById('blocked-attacks');
    const detectedVirusesDisplay = document.getElementById('detected-viruses');
    const safeConnectionsDisplay = document.getElementById('safe-connections');
    
    if (blockedAttacksDisplay) blockedAttacksDisplay.textContent = firewallStats.blockedAttacks;
    if (detectedVirusesDisplay) detectedVirusesDisplay.textContent = firewallStats.detectedViruses;
    if (safeConnectionsDisplay) safeConnectionsDisplay.textContent = firewallStats.safeConnections;
}

// Real-time Updates
setInterval(() => {
    if (firewallActive) {
        firewallStats.networkUptime++;
        firewallStats.totalTraffic += Math.floor(Math.random() * 100);
        
        // Random events
        if (Math.random() < 0.1) {
            firewallStats.safeConnections++;
            updateStatistics();
        }
    }
}, 5000);

// Initialize page
window.addEventListener('DOMContentLoaded', function() {
    addSecurityAlert('مرحباً بك في محاكي جدران حماية Sophos', 'info');
    updateRealTimeDashboard();
    updateStatistics();
    loadQuiz();
});


