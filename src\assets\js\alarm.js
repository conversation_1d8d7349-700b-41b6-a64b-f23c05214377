// Alarm System Simulator

let systemActive = false;
let quizScore = 0;
let answeredQuestions = 0;
const totalQuestions = 2;

// System Control Functions
function toggleSystem() {
    const toggleBtn = document.getElementById('system-toggle');
    const testBtn = document.getElementById('test-btn');
    const statusDiv = document.getElementById('system-status');
    const sensorTriggers = document.querySelectorAll('.sensor-trigger');
    const statusIndicators = document.querySelectorAll('.status-indicator');

    if (!systemActive) {
        // Activate system
        systemActive = true;
        toggleBtn.innerHTML = '<i class="fas fa-power-off ml-2"></i> إيقاف النظام';
        toggleBtn.classList.remove('bg-red-500', 'hover:bg-red-600');
        toggleBtn.classList.add('bg-green-500', 'hover:bg-green-600');
        
        testBtn.disabled = false;
        testBtn.classList.remove('opacity-50');
        
        statusDiv.className = 'bg-green-100 border border-green-200 p-4 rounded-lg text-center';
        statusDiv.innerHTML = '<p class="text-green-700">النظام نشط - جميع أجهزة الاستشعار تعمل</p>';
        
        // Enable sensor triggers
        sensorTriggers.forEach(trigger => {
            trigger.disabled = false;
            trigger.classList.remove('opacity-50');
        });

        // Change sensor indicators to green (active)
        statusIndicators.forEach(indicator => {
            indicator.classList.remove('bg-red-500');
            indicator.classList.add('bg-green-500');
        });

        addAlert('تم تشغيل النظام بنجاح', 'success');
        
    } else {
        // Deactivate system
        systemActive = false;
        toggleBtn.innerHTML = '<i class="fas fa-power-off ml-2"></i> تشغيل النظام';
        toggleBtn.classList.remove('bg-green-500', 'hover:bg-green-600');
        toggleBtn.classList.add('bg-red-500', 'hover:bg-red-600');
        
        testBtn.disabled = true;
        testBtn.classList.add('opacity-50');
        
        statusDiv.className = 'bg-red-100 border border-red-200 p-4 rounded-lg text-center';
        statusDiv.innerHTML = '<p class="text-red-700">النظام متوقف - انقر على "تشغيل النظام" للبدء</p>';
        
        // Disable sensor triggers
        sensorTriggers.forEach(trigger => {
            trigger.disabled = true;
            trigger.classList.add('opacity-50');
        });

        // Change sensor indicators to red (inactive)
        statusIndicators.forEach(indicator => {
            indicator.classList.remove('bg-green-500');
            indicator.classList.add('bg-red-500');
        });

        addAlert('تم إيقاف النظام', 'info');
    }
}

function testAlarm() {
    if (!systemActive) return;
    
    // Flash all indicators
    const statusIndicators = document.querySelectorAll('.status-indicator');
    statusIndicators.forEach(indicator => {
        indicator.classList.add('animate-pulse');
        indicator.classList.remove('bg-green-500');
        indicator.classList.add('bg-yellow-500');
    });

    // Play alarm sound simulation
    addAlert('🔔 اختبار الإنذار - جميع الأجهزة تعمل بشكل طبيعي', 'warning');
    
    // Reset indicators after 3 seconds
    setTimeout(() => {
        statusIndicators.forEach(indicator => {
            indicator.classList.remove('animate-pulse', 'bg-yellow-500');
            indicator.classList.add('bg-green-500');
        });
    }, 3000);
}

function triggerSensor(sensorType) {
    if (!systemActive) return;

    const sensorNames = {
        motion: 'مستشعر الحركة',
        door: 'مستشعر الباب',
        smoke: 'كاشف الدخان',
        temp: 'مستشعر الحرارة'
    };

    const alertTypes = {
        motion: 'تم اكتشاف حركة غير مصرح بها!',
        door: 'تم فتح باب بدون إذن!',
        smoke: '⚠️ تحذير: تم اكتشاف دخان!',
        temp: '🔥 خطر: ارتفاع درجة الحرارة!'
    };

    const colors = {
        motion: 'bg-blue-500',
        door: 'bg-green-500',
        smoke: 'bg-gray-600',
        temp: 'bg-red-500'
    };

    // Flash specific sensor
    const sensor = document.getElementById(`${sensorType}-sensor`);
    const indicator = sensor.querySelector('.status-indicator');
    
    indicator.classList.add('animate-pulse');
    indicator.classList.remove('bg-green-500');
    indicator.classList.add(colors[sensorType]);

    // Add alert
    addAlert(alertTypes[sensorType], sensorType === 'smoke' || sensorType === 'temp' ? 'danger' : 'warning');

    // Reset after 5 seconds
    setTimeout(() => {
        indicator.classList.remove('animate-pulse', colors[sensorType]);
        indicator.classList.add('bg-green-500');
    }, 5000);
}

function resetSystem() {
    // Clear alert log
    const alertLog = document.getElementById('alert-log');
    alertLog.innerHTML = '<div class="text-center text-gray-500 text-sm">لا توجد تنبيهات</div>';
    
    // Reset system state
    if (systemActive) {
        toggleSystem(); // Turn off system
    }
    
    addAlert('تم إعادة تعيين النظام', 'info');
}

function addAlert(message, type = 'info') {
    const alertLog = document.getElementById('alert-log');
    const timestamp = new Date().toLocaleTimeString('ar-SA');
    
    // Remove placeholder if exists
    const placeholder = alertLog.querySelector('.text-gray-500');
    if (placeholder) {
        placeholder.remove();
    }

    const alertDiv = document.createElement('div');
    alertDiv.className = `p-3 rounded-lg text-sm animate-fade-in ${getAlertStyle(type)}`;
    alertDiv.innerHTML = `
        <div class="flex justify-between items-start">
            <span>${message}</span>
            <span class="text-xs opacity-75">${timestamp}</span>
        </div>
    `;

    // Add to top of log
    alertLog.insertBefore(alertDiv, alertLog.firstChild);

    // Keep only last 10 alerts
    while (alertLog.children.length > 10) {
        alertLog.removeChild(alertLog.lastChild);
    }

    // Scroll to top
    alertLog.scrollTop = 0;
}

function getAlertStyle(type) {
    const styles = {
        success: 'bg-green-100 border border-green-200 text-green-800',
        warning: 'bg-yellow-100 border border-yellow-200 text-yellow-800',
        danger: 'bg-red-100 border border-red-200 text-red-800',
        info: 'bg-blue-100 border border-blue-200 text-blue-800'
    };
    return styles[type] || styles.info;
}

// Quiz Functions
function selectAnswer(element, isCorrect) {
    // Disable all options in this question
    const questionDiv = element.closest('.quiz-question');
    const allOptions = questionDiv.querySelectorAll('.quiz-option');
    
    allOptions.forEach(option => {
        option.style.pointerEvents = 'none';
        option.classList.add('opacity-60');
    });

    // Mark the selected answer
    if (isCorrect) {
        element.classList.add('border-green-500', 'bg-green-100');
        element.innerHTML += ' <i class="fas fa-check text-green-600 float-left"></i>';
        quizScore++;
    } else {
        element.classList.add('border-red-500', 'bg-red-100');
        element.innerHTML += ' <i class="fas fa-times text-red-600 float-left"></i>';
        
        // Show correct answer
        allOptions.forEach(option => {
            if (option.onclick && option.onclick.toString().includes('true')) {
                option.classList.add('border-green-500', 'bg-green-100');
                option.innerHTML += ' <i class="fas fa-check text-green-600 float-left"></i>';
            }
        });
    }

    answeredQuestions++;
    
    // Show results if all questions answered
    if (answeredQuestions >= totalQuestions) {
        setTimeout(() => {
            showQuizResults();
        }, 1000);
    }
}

function showQuizResults() {
    const resultsDiv = document.getElementById('quiz-results');
    const scoreSpan = document.getElementById('quiz-score');
    
    scoreSpan.textContent = quizScore;
    resultsDiv.classList.remove('hidden');
    resultsDiv.scrollIntoView({ behavior: 'smooth' });
    
    // Mark module as complete if score is good
    if (quizScore >= 1) {
        markModuleComplete();
    }
}

function markModuleComplete() {
    let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
    progress.alarm = true;
    localStorage.setItem('courseProgress', JSON.stringify(progress));
}

function completeModule() {
    markModuleComplete();
    showNotification('تم إكمال وحدة أنظمة الإنذار بنجاح!', 'success');
    setTimeout(() => {
        window.location.href = '../index.html';
    }, 1500);
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
    
    if (type === 'success') {
        notification.classList.add('bg-green-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-check-circle ml-2"></i>${message}`;
    } else if (type === 'error') {
        notification.classList.add('bg-red-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-exclamation-circle ml-2"></i>${message}`;
    } else {
        notification.classList.add('bg-blue-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-info-circle ml-2"></i>${message}`;
    }
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Animate out
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add welcome message
    setTimeout(() => {
        addAlert('مرحباً بك في محاكي أنظمة الإنذار', 'info');
    }, 1000);
});
