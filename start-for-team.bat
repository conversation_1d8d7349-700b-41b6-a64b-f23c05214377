@echo off
title IT Collaborator 2025 - Team Server
color 0A

echo =================================
echo  IT Collaborator 2025 - Team Server
echo =================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found!
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo Installing/updating dependencies...
pip install -r requirements.txt

echo.
echo Starting the application server...
echo.
echo TEAM ACCESS:
echo - Local: http://127.0.0.1:5000
echo - Network: http://[YOUR-IP]:5000
echo.
echo (Share the Network URL with your team members)
echo.
echo Press Ctrl+C to stop the server
echo.

python app.py
pause
