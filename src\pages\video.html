<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المراقبة بالفيديو - دورة الشبكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .module-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        }
        .interactive-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .interactive-card:hover {
            transform: translateY(-5px);
        }
        .quiz-option {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        .quiz-option:hover {
            background-color: #e0e7ff;
            transform: translateX(10px);
            border-color: #3b82f6;
        }
        .quiz-option.correct {
            background-color: #dcfce7;
            border-color: #22c55e;
        }
        .quiz-option.incorrect {
            background-color: #fee2e2;
            border-color: #ef4444;
        }
        .video-player {
            position: relative;
            background: #000;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        .video-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .video-player:hover .video-controls {
            opacity: 1;
        }
        .progress-bar {
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            margin-bottom: 10px;
            cursor: pointer;
        }
        .progress-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 2px;
            transition: width 0.1s ease;
        }
        .control-button {
            background: transparent;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        .control-button:hover {
            background: rgba(255,255,255,0.2);
        }
        .chat-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            padding: 15px;
            background: #f9fafb;
        }
        .chat-message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 10px;
            max-width: 80%;
        }
        .chat-message.user {
            background: #3b82f6;
            color: white;
            margin-left: auto;
        }
        .chat-message.system {
            background: #e5e7eb;
            color: #374151;
        }
        .floating-feedback {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 100;
        }
        .feedback-form {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 300px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        .feedback-form.show {
            transform: translateY(0);
        }
        .rating-stars {
            display: flex;
            gap: 5px;
            margin: 10px 0;
        }
        .rating-star {
            font-size: 24px;
            color: #d1d5db;
            cursor: pointer;
            transition: color 0.2s ease;
        }
        .rating-star:hover,
        .rating-star.active {
            color: #fbbf24;
        }
        .note-highlight {
            background: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .interactive-diagram {
            position: relative;
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .diagram-element {
            position: absolute;
            background: #3b82f6;
            color: white;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .diagram-element:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }
        .mini-game {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .drag-item {
            background: #3b82f6;
            color: white;
            padding: 10px;
            margin: 5px;
            border-radius: 8px;
            cursor: move;
            display: inline-block;
            transition: transform 0.2s ease;
        }
        .drag-item:hover {
            transform: scale(1.05);
        }
        .drop-zone {
            background: #f3f4f6;
            border: 2px dashed #9ca3af;
            border-radius: 8px;
            padding: 20px;
            margin: 10px;
            text-align: center;
            min-height: 60px;
            transition: all 0.3s ease;
        }
        .drop-zone.drag-over {
            background: #dbeafe;
            border-color: #3b82f6;
        }
        .drop-zone.correct {
            background: #dcfce7;
            border-color: #22c55e;
        }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        .notification.show {
            transform: translateX(0);
        }
        .notification.success {
            background: #10b981;
        }
        .notification.error {
            background: #ef4444;
        }
        .notification.info {
            background: #3b82f6;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-blue-100 text-gray-800">

    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-reverse space-x-2">
                    <i class="fas fa-video text-blue-600 text-2xl"></i>
                    <span class="font-bold text-xl text-gray-800">المراقبة بالفيديو</span>
                </div>
                <div class="flex space-x-reverse space-x-4">
                    <a href="../index.html" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-home ml-1"></i>
                        الرئيسية
                    </a>
                    <button onclick="completeModule()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-check ml-1"></i>
                        إكمال الوحدة
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header class="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-camera text-8xl"></i>
            </div>
            <h1 class="text-5xl font-bold mb-4">نظام المراقبة بالفيديو</h1>
            <p class="text-xl">تدريب شامل على أنظمة المراقبة وتقنياتها المتقدمة</p>
        </div>
    </header>

    <!-- Learning Objectives -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold mb-6 text-center">
                    <i class="fas fa-bullseye text-purple-600 ml-2"></i>
                    أهداف التعلم
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>فهم أنواع الكاميرات المختلفة وخصائصها</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>تعلم تركيب وتكوين أنظمة المراقبة</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>التعامل مع تقنيات التخزين والسحابة</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>تحليل بيانات الكاميرات الذكية والذكاء الاصطناعي</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Camera Types Interactive Section -->
    <section class="py-12 px-4 bg-gray-50">
        <div class="container mx-auto max-w-6xl">
            <h2 class="text-4xl font-bold text-center mb-12">أنواع الكاميرات الأمنية</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- IP Cameras -->
                <div class="interactive-card bg-white p-6 rounded-2xl shadow-lg text-center cursor-pointer" onclick="showCameraInfo('ip')">
                    <i class="fas fa-globe text-blue-600 text-5xl mb-4"></i>
                    <h3 class="text-xl font-bold mb-4">كاميرات IP</h3>
                    <p class="text-gray-600">كاميرات رقمية متصلة بالشبكة</p>
                </div>
                
                <!-- PTZ Cameras -->
                <div class="interactive-card bg-white p-6 rounded-2xl shadow-lg text-center cursor-pointer" onclick="showCameraInfo('ptz')">
                    <i class="fas fa-arrows-alt text-green-600 text-5xl mb-4"></i>
                    <h3 class="text-xl font-bold mb-4">كاميرات PTZ</h3>
                    <p class="text-gray-600">كاميرات متحركة قابلة للتحكم</p>
                </div>
                
                <!-- Thermal Cameras -->
                <div class="interactive-card bg-white p-6 rounded-2xl shadow-lg text-center cursor-pointer" onclick="showCameraInfo('thermal')">
                    <i class="fas fa-thermometer-full text-red-600 text-5xl mb-4"></i>
                    <h3 class="text-xl font-bold mb-4">الكاميرات الحرارية</h3>
                    <p class="text-gray-600">تعمل بالأشعة تحت الحمراء</p>
                </div>
                
                <!-- AI Cameras -->
                <div class="interactive-card bg-white p-6 rounded-2xl shadow-lg text-center cursor-pointer" onclick="showCameraInfo('ai')">
                    <i class="fas fa-brain text-purple-600 text-5xl mb-4"></i>
                    <h3 class="text-xl font-bold mb-4">الكاميرات الذكية</h3>
                    <p class="text-gray-600">مزودة بالذكاء الاصطناعي</p>
                </div>
            </div>
            
            <!-- Camera Info Panel -->
            <div id="camera-info-panel" class="mt-8 bg-white rounded-2xl shadow-lg p-8 hidden">
                <h3 id="camera-info-title" class="text-2xl font-bold mb-4"></h3>
                <div id="camera-info-content" class="text-gray-700"></div>
            </div>
        </div>
    </section>


    <!-- Interactive Network Diagram -->
    <section class="py-12 px-4 bg-gray-50">
        <div class="container mx-auto max-w-6xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-network-wired text-purple-600 ml-2"></i>
                    مخطط شبكة المراقبة التفاعلي
                </h2>
                
                <div class="interactive-diagram" style="height: 400px; position: relative;">
                    <div class="diagram-element" style="top: 50px; left: 50px;" onclick="showDiagramInfo('router')">
                        <i class="fas fa-router text-2xl mb-2"></i>
                        <div>الراوتر</div>
                    </div>
                    
                    <div class="diagram-element" style="top: 50px; right: 50px;" onclick="showDiagramInfo('nvr')">
                        <i class="fas fa-server text-2xl mb-2"></i>
                        <div>NVR</div>
                    </div>
                    
                    <div class="diagram-element" style="bottom: 50px; left: 100px;" onclick="showDiagramInfo('camera1')">
                        <i class="fas fa-video text-2xl mb-2"></i>
                        <div>كاميرا 1</div>
                    </div>
                    
                    <div class="diagram-element" style="bottom: 50px; right: 100px;" onclick="showDiagramInfo('camera2')">
                        <i class="fas fa-video text-2xl mb-2"></i>
                        <div>كاميرا 2</div>
                    </div>
                    
                    <div class="diagram-element" style="top: 150px; left: 50%; transform: translateX(-50%);" onclick="showDiagramInfo('switch')">
                        <i class="fas fa-ethernet text-2xl mb-2"></i>
                        <div>Switch</div>
                    </div>
                </div>
                
                <div id="diagram-info" class="mt-6 p-4 bg-blue-50 rounded-lg hidden">
                    <h4 id="diagram-info-title" class="text-lg font-bold mb-2"></h4>
                    <p id="diagram-info-content"></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Drag and Drop Mini Game -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-6xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-gamepad text-green-600 ml-2"></i>
                    لعبة ترتيب مكونات النظام
                </h2>
                
                <div class="mini-game">
                    <p class="text-center mb-6 text-lg">اسحب وأفلت المكونات في الترتيب الصحيح لإعداد نظام المراقبة:</p>
                    
                    <div class="mb-6">
                        <h3 class="text-xl font-bold mb-4">المكونات المتاحة:</h3>
                        <div class="flex flex-wrap gap-4">
                            <div class="drag-item" draggable="true" data-component="camera-support">
                                <i class="fas fa-tools ml-2"></i>
                                تركيب حاملات الكاميرات
                            </div>
                            <div class="drag-item" draggable="true" data-component="camera-install">
                                <i class="fas fa-video ml-2"></i>
                                تركيب الكاميرات
                            </div>
                            <div class="drag-item" draggable="true" data-component="cables">
                                <i class="fas fa-ethernet ml-2"></i>
                                توصيل الكابلات
                            </div>
                            <div class="drag-item" draggable="true" data-component="nvr">
                                <i class="fas fa-server ml-2"></i>
                                تركيب NVR (مسجل الفيديو الشبكي)
                            </div>
                            <div class="drag-item" draggable="true" data-component="software">
                                <i class="fas fa-desktop ml-2"></i>
                                تثبيت وتشغيل البرامج
                            </div>
                            <div class="drag-item" draggable="true" data-component="testing">
                                <i class="fas fa-check-circle ml-2"></i>
                                اختبار النظام والتأكد من التشغيل
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div class="drop-zone" data-step="1">
                            <div class="text-gray-500">الخطوة 1</div>
                        </div>
                        <div class="drop-zone" data-step="2">
                            <div class="text-gray-500">الخطوة 2</div>
                        </div>
                        <div class="drop-zone" data-step="3">
                            <div class="text-gray-500">الخطوة 3</div>
                        </div>
                        <div class="drop-zone" data-step="4">
                            <div class="text-gray-500">الخطوة 4</div>
                        </div>
                        <div class="drop-zone" data-step="5">
                            <div class="text-gray-500">الخطوة 5</div>
                        </div>
                        <div class="drop-zone" data-step="6">
                            <div class="text-gray-500">الخطوة 6</div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-6">
                        <button onclick="checkGameAnswer()" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                            <i class="fas fa-check ml-2"></i>
                            فحص الإجابة
                        </button>
                        <button onclick="resetGame()" class="bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors ml-4">
                            <i class="fas fa-redo ml-2"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Floating Feedback Form -->
    <div class="floating-feedback">
        <button class="bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors" onclick="toggleFeedbackForm()">
            <i class="fas fa-comment-dots"></i>
        </button>
        <div class="feedback-form" id="feedback-form">
            <h3 class="text-xl font-bold mb-4">
                <i class="fas fa-star text-yellow-500 ml-2"></i>
                قيم المحتوى
            </h3>
            <div class="rating-stars" id="rating-stars">
                <span class="rating-star" onclick="rate(1)">★</span>
                <span class="rating-star" onclick="rate(2)">★</span>
                <span class="rating-star" onclick="rate(3)">★</span>
                <span class="rating-star" onclick="rate(4)">★</span>
                <span class="rating-star" onclick="rate(5)">★</span>
            </div>
            <textarea id="feedback-text" class="w-full p-3 border rounded-lg mb-4" rows="4" placeholder="شاركنا رأيك في المحتوى..."></textarea>
            <div class="flex justify-between">
                <button onclick="submitFeedback()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                    <i class="fas fa-paper-plane ml-2"></i>
                    إرسال
                </button>
                <button onclick="toggleFeedbackForm()" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                    <i class="fas fa-times ml-2"></i>
                    إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- Camera Setup Simulator -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-6xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-tools text-blue-600 ml-2"></i>
                    محاكي إعداد كاميرا المراقبة
                </h2>
                
                <div class="text-center mb-8">
                    <button id="start-simulation" onclick="startSimulation()" class="bg-green-500 text-white px-8 py-3 rounded-lg hover:bg-green-600 transition-colors text-lg font-bold">
                        <i class="fas fa-play ml-2"></i>
                        بدء المحاكاة
                    </button>
                </div>
                
                <div id="camera-model" class="hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <div id="step-location" class="border-2 border-gray-300 p-6 rounded-lg transition-all duration-300">
                            <div class="text-center">
                                <i class="fas fa-map-marker-alt text-4xl text-blue-500 mb-4"></i>
                                <h3 class="font-bold text-xl mb-4">اختيار الموقع</h3>
                                <p class="text-gray-600 mb-4">تحديد الموقع الأمثل لتركيب الكاميرا</p>
                                <button id="location-btn" onclick="executeSetup('location')" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors" disabled>
                                    تنفيذ
                                </button>
                            </div>
                        </div>
                        
                        <div id="step-mount" class="border-2 border-gray-300 p-6 rounded-lg transition-all duration-300">
                            <div class="text-center">
                                <i class="fas fa-screwdriver text-4xl text-orange-500 mb-4"></i>
                                <h3 class="font-bold text-xl mb-4">تركيب الكاميرا</h3>
                                <p class="text-gray-600 mb-4">تثبيت الكاميرا في الموقع المحدد</p>
                                <button id="mount-btn" onclick="executeSetup('mount')" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors" disabled>
                                    تنفيذ
                                </button>
                            </div>
                        </div>
                        
                        <div id="step-connect" class="border-2 border-gray-300 p-6 rounded-lg transition-all duration-300">
                            <div class="text-center">
                                <i class="fas fa-ethernet text-4xl text-green-500 mb-4"></i>
                                <h3 class="font-bold text-xl mb-4">توصيل الكاميرا</h3>
                                <p class="text-gray-600 mb-4">ربط الكاميرا بالشبكة والطاقة</p>
                                <button id="connect-btn" onclick="executeSetup('connect')" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors" disabled>
                                    تنفيذ
                                </button>
                            </div>
                        </div>
                        
                        <div id="step-configure" class="border-2 border-gray-300 p-6 rounded-lg transition-all duration-300">
                            <div class="text-center">
                                <i class="fas fa-cogs text-4xl text-purple-500 mb-4"></i>
                                <h3 class="font-bold text-xl mb-4">تكوين النظام</h3>
                                <p class="text-gray-600 mb-4">ضبط إعدادات الكاميرا والنظام</p>
                                <button id="configure-btn" onclick="executeSetup('configure')" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition-colors" disabled>
                                    تنفيذ
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div id="success-message" class="text-center bg-green-50 border border-green-200 rounded-lg p-6 mb-6 hidden">
                        <i class="fas fa-check-circle text-green-600 text-4xl mb-4"></i>
                        <h3 class="text-2xl font-bold text-green-800 mb-2">🎉 اكتمل الإعداد بنجاح!</h3>
                        <p class="text-green-700">تم تركيب وتكوين كاميرا المراقبة بنجاح وهي جاهزة للعمل</p>
                    </div>
                    
                    <div class="bg-gray-100 p-6 rounded-lg">
                        <div class="flex justify-between items-center mb-4">
                            <span class="font-bold text-lg">التقدم:</span>
                            <span id="progress-percentage" class="font-bold text-blue-600">0%</span>
                        </div>
                        <div class="w-full bg-gray-300 rounded-full h-4 mb-4">
                            <div id="setup-progress" class="h-4 rounded-full bg-blue-500 transition-all duration-500" style="width: 0%;"></div>
                        </div>
                        <p id="progress-text" class="text-gray-700 text-center">انقر على "بدء المحاكاة" لبدء العمل</p>
                    </div>
                    
                    <div class="text-center mt-6">
                        <button onclick="resetSimulation()" class="bg-red-500 text-white px-6 py-2 rounded-lg hover:bg-red-600 transition-colors">
                            <i class="fas fa-redo ml-2"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quiz Section -->
    <section class="py-12 px-4 bg-gray-50">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-question-circle text-purple-600 ml-2"></i>
                    اختبر معرفتك
                </h2>
                <div id="quiz-container"></div>
                <div class="text-center mt-6">
                    <button onclick="loadQuiz()" class="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors">
                        <i class="fas fa-play ml-2"></i>
                        بدء الاختبار
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2025 دورة الشبكات وأمن المعلومات</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/video.js"></script>
    <script>
        function completeModule() {
            let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
            progress.video = true;
            localStorage.setItem('courseProgress', JSON.stringify(progress));
            alert('تم إكمال وحدة المراقبة بالفيديو بنجاح! 🎉');
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 1000);
        }
    </script>
</body>
</html>
