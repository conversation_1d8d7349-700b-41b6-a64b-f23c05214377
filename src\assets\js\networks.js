// Interactive Network Simulator

document.addEventListener('DOMContentLoaded', function() {
    const sendPacketBtn = document.getElementById('send-packet-btn');

    sendPacketBtn.addEventListener('click', function() {
        showNotification('حزمة البيانات أرسلت بنجاح!', 'success');
    });
});

function connectDevices() {
    // Show connection lines
    document.querySelectorAll('#network-canvas line').forEach(line => {
        line.style.opacity = '1';
    });

    // Enable Send Packet button
    document.getElementById('send-packet-btn').disabled = false;

    // Update status
    const status = document.getElementById('network-status');
    status.textContent = 'جميع الأجهزة متصلة! جاهز لإرسال البيانات.';
    status.classList.remove('text-gray-600');
    status.classList.add('text-green-600');
}

function sendPacket() {
    showNotification('تم إرسال حزمة البيانات!', 'success');
}

function resetNetwork() {
    // Reset connection lines
    document.querySelectorAll('#network-canvas line').forEach(line => {
        line.style.opacity = '0';
    });

    // Disable Send Packet button
    document.getElementById('send-packet-btn').disabled = true;

    // Update status
    const status = document.getElementById('network-status');
    status.textContent = 'انقر على "ربط الأجهزة" لبدء المحاكي';
    status.classList.add('text-gray-600');
    status.classList.remove('text-green-600');
}

function showLayerInfo(layer) {
    const layerInfo = {
        1: {
            title: 'الطبقة الفيزيائية (Physical)',
            description: 'الطبقة المسؤولة عن النقل الفيزيائي للإشارات بين الأجهزة.'
        },
        2: {
            title: 'طبقة ربط البيانات (Data Link)',
            description: 'توفر وسائط الإرسال الآمنة وتتحقق من وصول البيانات بدون أخطاء.'
        },
        3: {
            title: 'طبقة الشبكة (Network)',
            description: 'توفر توجيه البيانات بين الشبكات المختلفة.'
        },
        4: {
            title: 'طبقة النقل (Transport)',
            description: 'تتحكم في نقل البيانات وتضمن وصولها كاملاً وبالترتيب الصحيح.'
        },
        5: {
            title: 'طبقة الجلسة (Session)',
            description: 'توفر إدارة الجلسات بين التطبيقات المختلفة.'
        },
        6: {
            title: 'طبقة العرض (Presentation)',
            description: 'تقوم بتنسيق البيانات لعرضها بشكل سليم على المستخدم.'
        },
        7: {
            title: 'طبقة التطبيق (Application)',
            description: 'الطبقة الأقرب للمستخدم والمسؤولة عن التواصل مع التطبيقات مباشرة.'
        },
    };

    const infoPanel = document.getElementById('layer-info');
    const title = document.getElementById('layer-title');
    const description = document.getElementById('layer-description');

    title.textContent = layerInfo[layer].title;
    description.textContent = layerInfo[layer].description;
    infoPanel.classList.remove('hidden');
    infoPanel.scrollIntoView({ behavior: 'smooth' });
}

function calculateIP() {
    const ipInput = document.getElementById('ip-input').value;
    const subnetMask = parseInt(document.getElementById('subnet-mask').value);

    if (!validateIP(ipInput)) {
        showNotification('عنوان IP غير صالح.', 'error');
        return;
    }

    const ipParts = ipInput.split('.').map(part => parseInt(part, 10));
    const subnetBinary = '1'.repeat(subnetMask) + '0'.repeat(32 - subnetMask);

    const networkAddress = ipParts.map((part, index) => part & parseInt(subnetBinary.slice(index * 8, (index + 1) * 8), 2)).join('.');
    const broadcastAddress = ipParts.map((part, index) => part | (255 - parseInt(subnetBinary.slice(index * 8, (index + 1) * 8), 2))).join('.');
    const firstHost = networkAddress.split('.');
    firstHost[3] = parseInt(firstHost[3]) + 1;
    const lastHost = broadcastAddress.split('.');
    lastHost[3] = parseInt(lastHost[3]) - 1;
    const hostCount = Math.pow(2, 32 - subnetMask) - 2;

    document.getElementById('network-address').textContent = networkAddress;
    document.getElementById('broadcast-address').textContent = broadcastAddress;
    document.getElementById('first-host').textContent = firstHost.join('.');
    document.getElementById('last-host').textContent = lastHost.join('.');
    document.getElementById('host-count').textContent = hostCount;

    showNotification('تم حساب عناوين IP بنجاح!', 'success');
}

function validateIP(ip) {
    const regex = /^([0-9]{1,3}\.){3}[0-9]{1,3}$/;
    return regex.test(ip) && ip.split('.').every(part => parseInt(part, 10) <= 255);
}

function completeModule() {
    let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
    progress.networks = true;
    localStorage.setItem('courseProgress', JSON.stringify(progress));
    
    showNotification('تم إكمال وحدة أساسيات الشبكات بنجاح!', 'success');
    setTimeout(() => {
        window.location.href = '../index.html';
    }, 1500);
}

// Notification System (shared function)
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
    
    if (type === 'success') {
        notification.classList.add('bg-green-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-check-circle ml-2"></i>${message}`;
    } else if (type === 'error') {
        notification.classList.add('bg-red-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-exclamation-circle ml-2"></i>${message}`;
    } else {
        notification.classList.add('bg-blue-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-info-circle ml-2"></i>${message}`;
    }
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Animate out
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Simulate ipconfig command
function simulateIpconfig() {
    const output = `
Windows IP Configuration

Ethernet adapter Local Area Connection:

   Connection-specific DNS Suffix  . : example.local
   Link-local IPv6 Address . . . . . : fe80::1234:5678:9abc:def0%11
   IPv4 Address. . . . . . . . . . . : ************
   Subnet Mask . . . . . . . . . . . : *************
   Default Gateway . . . . . . . . . : ***********

Wireless LAN adapter Wi-Fi:

   Connection-specific DNS Suffix  . : 
   Link-local IPv6 Address . . . . . : fe80::abcd:ef01:2345:6789%3
   IPv4 Address. . . . . . . . . . . : ************
   Subnet Mask . . . . . . . . . . . : *************
   Default Gateway . . . . . . . . . : ***********
`;
    displayOutput(output);
    showNotification('تم تشغيل ipconfig بنجاح!', 'success');
}

// Simulate ping command
function simulatePing() {
    const output = `
Pinging google.com [**************] with 32 bytes of data:

Reply from **************: bytes=32 time=28ms TTL=117
Reply from **************: bytes=32 time=29ms TTL=117
Reply from **************: bytes=32 time=27ms TTL=117
Reply from **************: bytes=32 time=28ms TTL=117

Ping statistics for **************:
    Packets: Sent = 4, Received = 4, Lost = 0 (0% loss),
Approximate round trip times in milli-seconds:
    Minimum = 27ms, Maximum = 29ms, Average = 28ms
`;
    displayOutput(output);
    showNotification('تم تشغيل ping بنجاح!', 'success');
}

// Simulate tracert command
function simulateTracert() {
    const output = `
Tracing route to google.com [**************]
over a maximum of 30 hops:

  1    <1 ms    <1 ms    <1 ms  ***********
  2     8 ms     7 ms     8 ms  ********
  3    12 ms    11 ms    12 ms  isp-gateway.net [***********]
  4    18 ms    19 ms    18 ms  core-router.isp.net [************]
  5    25 ms    26 ms    25 ms  google-peering.net [************]
  6    28 ms    29 ms    28 ms  google.com [**************]

Trace complete.
`;
    displayOutput(output);
    showNotification('تم تشغيل tracert بنجاح!', 'success');
}

// Display output in the troubleshooting section
function displayOutput(output) {
    const outputDiv = document.getElementById('troubleshooting-output');
    outputDiv.innerHTML = `<pre class="whitespace-pre-wrap text-green-400">${output}</pre>`;
    outputDiv.scrollTop = outputDiv.scrollHeight;
}
