@echo off
echo Replacing all corrupted HTML files with clean Arabic text versions...
echo.

REM Stop the server first
echo Stopping Flask server...
taskkill /f /im python.exe >nul 2>&1

echo.
echo Replacing files...

copy "src\pages\fire-fixed.html" "src\pages\fire.html" >nul
if %errorlevel%==0 (
    echo ✓ fire.html replaced with clean version
) else (
    echo ✗ Failed to replace fire.html
)

copy "src\pages\video-fixed.html" "src\pages\video.html" >nul
if %errorlevel%==0 (
    echo ✓ video.html replaced with clean version
) else (
    echo ✗ Failed to replace video.html
)

copy "src\pages\fire-protection-fixed.html" "src\pages\fire-protection.html" >nul
if %errorlevel%==0 (
    echo ✓ fire-protection.html replaced with clean version
) else (
    echo ✗ Failed to replace fire-protection.html
)

copy "src\pages\sophos-firewalls-fixed.html" "src\pages\sophos-firewalls.html" >nul
if %errorlevel%==0 (
    echo ✓ sophos-firewalls.html replaced with clean version
) else (
    echo ✗ Failed to replace sophos-firewalls.html
)

echo.
echo All files have been replaced with clean Arabic text versions!
echo.
echo Starting Flask server...
python app.py
