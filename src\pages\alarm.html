﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أنظمة الإنذار - دورة الشبكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .module-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        }
        
        .interactive-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .interactive-card:hover {
            transform: translateY(-5px);
        }
        
        .alarm-device {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .alarm-device:hover {
            transform: scale(1.05);
        }
        
        .alarm-device.active {
            background-color: #ef4444;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .quiz-option {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .quiz-option:hover {
            background-color: #e0e7ff;
            transform: translateX(10px);
            border-color: #3b82f6;
        }
        
        .quiz-option.correct {
            background-color: #dcfce7;
            border-color: #22c55e;
        }
        
        .quiz-option.incorrect {
            background-color: #fee2e2;
            border-color: #ef4444;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-yellow-50 to-orange-100 text-gray-800">

    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-reverse space-x-2">
                    <i class="fas fa-exclamation-triangle text-yellow-600 text-2xl"></i>
                    <span class="font-bold text-xl text-gray-800">أنظمة الإنذار</span>
                </div>
                <div class="flex space-x-reverse space-x-4">
                    <a href="/" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-home ml-1"></i>
                        الرئيسية
                    </a>
                    <button onclick="completeModule()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-check ml-1"></i>
                        إكمال الوحدة
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header class="bg-gradient-to-r from-yellow-600 via-orange-600 to-red-600 text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-bell text-8xl"></i>
            </div>
            <h1 class="text-5xl font-bold mb-4">أنظمة الإنذار</h1>
            <p class="text-xl">اكتشاف التهديدات مبكراً وحماية الأرواح والممتلكات</p>
        </div>
    </header>

    <!-- Learning Objectives -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold mb-6 text-center">
                    <i class="fas fa-bullseye text-orange-600 ml-2"></i>
                    أهداف التعلم
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>فهم أنواع أنظمة الإنذار المختلفة</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>كيفية عمل أجهزة الاستشعار</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>تصميم خطة أمنية شاملة</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>التعامل مع الإنذارات الكاذبة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Alarm Types -->
    <section class="py-12 px-4 bg-gray-50">
        <div class="container mx-auto max-w-6xl">
            <h2 class="text-4xl font-bold text-center mb-12">أنواع أنظمة الإنذار</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                
                <!-- Security Alarms -->
                <div class="interactive-card bg-white p-6 rounded-2xl shadow-lg text-center transform hover:scale-105 transition-all duration-300">
                    <div>
                        <i class="fas fa-shield-alt text-blue-600 text-5xl mb-4"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">الإنذار الأمني</h3>
                    <p class="text-gray-600 mb-4">حماية المباني من التسلل والسرقة</p>
                    <div class="space-y-2 text-sm">
                        <div class="bg-blue-50 p-2 rounded">• مستشعرات الحركة</div>
                        <div class="bg-blue-50 p-2 rounded">• مستشعرات الأبواب</div>
                        <div class="bg-blue-50 p-2 rounded">• كاميرات المراقبة</div>
                    </div>
                </div>

                <!-- Fire Alarms -->
                <div class="interactive-card bg-white p-6 rounded-2xl shadow-lg text-center transform hover:scale-105 transition-all duration-300">
                    <div>
                        <i class="fas fa-fire-alt text-red-600 text-5xl mb-4"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">إنذار الحريق</h3>
                    <p class="text-gray-600 mb-4">الكشف المبكر عن الحرائق</p>
                    <div class="space-y-2 text-sm">
                        <div class="bg-red-50 p-2 rounded">• كاشفات الدخان</div>
                        <div class="bg-red-50 p-2 rounded">• كاشفات الحرارة</div>
                        <div class="bg-red-50 p-2 rounded">• صفارات الإنذار</div>
                    </div>
                </div>

                <!-- Medical Alarms -->
                <div class="interactive-card bg-white p-6 rounded-2xl shadow-lg text-center transform hover:scale-105 transition-all duration-300">
                    <div>
                        <i class="fas fa-heartbeat text-green-600 text-5xl mb-4"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">الإنذار الطبي</h3>
                    <p class="text-gray-600 mb-4">مراقبة الحالة الصحية</p>
                    <div class="space-y-2 text-sm">
                        <div class="bg-green-50 p-2 rounded">• مراقبة النبض</div>
                        <div class="bg-green-50 p-2 rounded">• أزرار الطوارئ</div>
                        <div class="bg-green-50 p-2 rounded">• أجهزة التنفس</div>
                    </div>
                </div>

                <!-- Industrial Alarms -->
                <div class="interactive-card bg-white p-6 rounded-2xl shadow-lg text-center transform hover:scale-105 transition-all duration-300">
                    <div>
                        <i class="fas fa-industry text-purple-600 text-5xl mb-4"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-4">الإنذار الصناعي</h3>
                    <p class="text-gray-600 mb-4">مراقبة العمليات الصناعية</p>
                    <div class="space-y-2 text-sm">
                        <div class="bg-purple-50 p-2 rounded">• مراقبة الضغط</div>
                        <div class="bg-purple-50 p-2 rounded">• كشف التسرب</div>
                        <div class="bg-purple-50 p-2 rounded">• إنذار الطوارئ</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Alarm System Simulator -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-6xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-home text-blue-600 ml-2"></i>
                    محاكي نظام الإنذار المنزلي
                </h2>
                
                <!-- House Layout -->
                <div class="bg-gray-100 rounded-lg p-8 mb-6 relative" id="house-layout">
                    <div class="grid grid-cols-3 gap-4 h-64">
                        
                        <!-- Living Room -->
                        <div class="bg-white border-2 border-gray-300 rounded p-4 text-center">
                            <h4 class="font-bold mb-2">غرفة المعيشة</h4>
                            <div class="alarm-device bg-gray-200 p-2 rounded mb-2" data-device="motion1" onclick="toggleAlarm('motion1')">
                                <i class="fas fa-walking text-blue-500"></i>
                                <p class="text-xs">حركة</p>
                            </div>
                            <div class="alarm-device bg-gray-200 p-2 rounded" data-device="smoke1" onclick="toggleAlarm('smoke1')">
                                <i class="fas fa-smoke text-gray-500"></i>
                                <p class="text-xs">دخان</p>
                            </div>
                        </div>

                        <!-- Kitchen -->
                        <div class="bg-white border-2 border-gray-300 rounded p-4 text-center">
                            <h4 class="font-bold mb-2">المطبخ</h4>
                            <div class="alarm-device bg-gray-200 p-2 rounded mb-2" data-device="gas1" onclick="toggleAlarm('gas1')">
                                <i class="fas fa-burn text-orange-500"></i>
                                <p class="text-xs">غاز</p>
                            </div>
                            <div class="alarm-device bg-gray-200 p-2 rounded" data-device="heat1" onclick="toggleAlarm('heat1')">
                                <i class="fas fa-thermometer-full text-red-500"></i>
                                <p class="text-xs">حرارة</p>
                            </div>
                        </div>

                        <!-- Bedroom -->
                        <div class="bg-white border-2 border-gray-300 rounded p-4 text-center">
                            <h4 class="font-bold mb-2">غرفة النوم</h4>
                            <div class="alarm-device bg-gray-200 p-2 rounded mb-2" data-device="window1" onclick="toggleAlarm('window1')">
                                <i class="fas fa-door-open text-green-500"></i>
                                <p class="text-xs">نافذة</p>
                            </div>
                            <div class="alarm-device bg-gray-200 p-2 rounded" data-device="motion2" onclick="toggleAlarm('motion2')">
                                <i class="fas fa-walking text-blue-500"></i>
                                <p class="text-xs">حركة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Control Panel -->
                <div class="flex justify-center space-x-reverse space-x-4 mb-6">
                    <button onclick="armSystem()" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-shield-alt ml-2"></i>
                        تفعيل النظام
                    </button>
                    <button onclick="disarmSystem()" class="bg-yellow-500 text-white px-6 py-3 rounded-lg hover:bg-yellow-600 transition-colors">
                        <i class="fas fa-shield-alt ml-2"></i>
                        إلغاء التفعيل
                    </button>
                    <button onclick="testSystem()" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-vial ml-2"></i>
                        اختبار النظام
                    </button>
                    <button onclick="resetSystem()" class="bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-redo ml-2"></i>
                        إعادة تعيين
                    </button>
                </div>

                <!-- System Status -->
                <div id="system-status" class="bg-gray-100 p-4 rounded-lg text-center">
                    <p class="text-gray-600">النظام في وضع الاستعداد - انقر على "تفعيل النظام" للبدء</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2025 دورة الشبكات وأمن المعلومات</p>
        </div>
    </footer>

    <script>
        let systemArmed = false;
        let activeAlarms = new Set();

        function completeModule() {
            let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
            progress.alarm = true;
            localStorage.setItem('courseProgress', JSON.stringify(progress));
            
            alert('تم إكمال وحدة أنظمة الإنذار بنجاح! 🎉');
            
            // Redirect back to main page
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        }

        function toggleAlarm(deviceId) {
            if (!systemArmed) {
                updateStatus('⚠️ النظام غير مفعل - فعل النظام أولاً', 'text-yellow-600');
                return;
            }

            const device = document.querySelector(`[data-device="${deviceId}"]`);
            
            if (activeAlarms.has(deviceId)) {
                // Turn off alarm
                device.classList.remove('active');
                activeAlarms.delete(deviceId);
                updateStatus('✅ تم إيقاف الإنذار من ' + getDeviceName(deviceId), 'text-green-600');
            } else {
                // Turn on alarm
                device.classList.add('active');
                activeAlarms.add(deviceId);
                updateStatus('🚨 إنذار من ' + getDeviceName(deviceId) + '!', 'text-red-600 font-bold');
                
                // Play alarm sound (if available)
                playAlarmSound();
            }
        }

        function getDeviceName(deviceId) {
            const names = {
                'motion1': 'مستشعر الحركة - غرفة المعيشة',
                'smoke1': 'كاشف الدخان - غرفة المعيشة',
                'gas1': 'مستشعر الغاز - المطبخ',
                'heat1': 'مستشعر الحرارة - المطبخ',
                'window1': 'مستشعر النافذة - غرفة النوم',
                'motion2': 'مستشعر الحركة - غرفة النوم'
            };
            return names[deviceId] || 'جهاز غير معروف';
        }

        function armSystem() {
            systemArmed = true;
            updateStatus('🟢 تم تفعيل نظام الإنذار - جميع المستشعرات نشطة', 'text-green-600 font-bold');
        }

        function disarmSystem() {
            systemArmed = false;
            activeAlarms.clear();
            
            // Remove active status from all devices
            document.querySelectorAll('.alarm-device').forEach(device => {
                device.classList.remove('active');
            });
            
            updateStatus('🔴 تم إلغاء تفعيل نظام الإنذار', 'text-red-600');
        }

        function testSystem() {
            if (!systemArmed) {
                updateStatus('⚠️ فعل النظام أولاً لإجراء الاختبار', 'text-yellow-600');
                return;
            }

            updateStatus('🔧 جاري اختبار جميع المستشعرات...', 'text-blue-600');
            
            setTimeout(() => {
                updateStatus('✅ اختبار النظام مكتمل - جميع المستشعرات تعمل بشكل صحيح', 'text-green-600');
            }, 2000);
        }

        function resetSystem() {
            systemArmed = false;
            activeAlarms.clear();
            
            // Remove active status from all devices
            document.querySelectorAll('.alarm-device').forEach(device => {
                device.classList.remove('active');
            });
            
            updateStatus('النظام في وضع الاستعداد - انقر على "تفعيل النظام" للبدء', 'text-gray-600');
        }

        function updateStatus(message, className) {
            const statusDiv = document.getElementById('system-status');
            statusDiv.innerHTML = `<p class="${className}">${message}</p>`;
        }

        function playAlarmSound() {
            // Create a simple beep sound using Web Audio API
            if (typeof(AudioContext) !== "undefined" || typeof(webkitAudioContext) !== "undefined") {
                try {
                    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    const oscillator = audioContext.createOscillator();
                    const gainNode = audioContext.createGain();
                    
                    oscillator.connect(gainNode);
                    gainNode.connect(audioContext.destination);
                    
                    oscillator.frequency.value = 800;
                    oscillator.type = 'square';
                    
                    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                    
                    oscillator.start(audioContext.currentTime);
                    oscillator.stop(audioContext.currentTime + 0.5);
                } catch (e) {
                    // Audio not available, silently continue
                }
            }
        }
    </script>
</body>
</html>
