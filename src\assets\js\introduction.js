// Introduction Page Functionality

function completeModule() {
    markModuleComplete();
    showNotification('تم إكمال وحدة المقدمة بنجاح!', 'success');
    setTimeout(() => {
        window.location.href = '../index.html';
    }, 1500);
}

function markModuleComplete() {
    let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
    progress.introduction = true;
    localStorage.setItem('courseProgress', JSON.stringify(progress));
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
    
    if (type === 'success') {
        notification.classList.add('bg-green-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-check-circle ml-2"></i>${message}`;
    } else if (type === 'error') {
        notification.classList.add('bg-red-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-exclamation-circle ml-2"></i>${message}`;
    } else {
        notification.classList.add('bg-blue-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-info-circle ml-2"></i>${message}`;
    }
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Animate out
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add animations to sections on scroll
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
            }
        });
    });

    // Observe all major sections
    document.querySelectorAll('section').forEach((section) => {
        observer.observe(section);
    });

    // Add hover effects to interactive elements
    document.querySelectorAll('.feature-card, .concept-card, .path-step').forEach(card => {
        card.classList.add('transform', 'transition-all', 'duration-300', 'hover:scale-105');
    });

    // Welcome message
    setTimeout(() => {
        showNotification('مرحباً بك في دورة الشبكات وأمن المعلومات!', 'info');
    }, 1000);
});
