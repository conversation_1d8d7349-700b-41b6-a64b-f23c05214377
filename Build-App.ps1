# PowerShell script to build IT Collaborator 2025 executable
Write-Host "Building IT Collaborator 2025 executable..." -ForegroundColor Green

try {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install

    Write-Host "Building Windows executable..." -ForegroundColor Yellow
    npm run build-win

    Write-Host "Build complete! Check the 'dist' folder for the executable." -ForegroundColor Green
    Write-Host "Press any key to continue..." -ForegroundColor Cyan
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
catch {
    Write-Host "Error occurred: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Press any key to continue..." -ForegroundColor Cyan
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}
