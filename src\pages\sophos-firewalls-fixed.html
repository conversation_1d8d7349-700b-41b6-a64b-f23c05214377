<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جدران حماية Sophos - دورة الشبكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .module-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        }
        
        .interactive-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .interactive-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-cyan-50 to-blue-100 text-gray-800">

    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-reverse space-x-2">
                    <i class="fas fa-shield-alt text-cyan-600 text-2xl"></i>
                    <span class="font-bold text-xl text-gray-800">جدران حماية Sophos</span>
                </div>
                <div class="flex space-x-reverse space-x-4">
                    <a href="/" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-home ml-1"></i>
                        الرئيسية
                    </a>
                    <button onclick="completeModule()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-check ml-1"></i>
                        إكمال الوحدة
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header class="bg-gradient-to-r from-cyan-600 via-blue-600 to-cyan-800 text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-shield-alt text-8xl"></i>
            </div>
            <h1 class="text-5xl font-bold mb-4">جدران حماية Sophos</h1>
            <p class="text-xl">أنظمة الحماية المتقدمة للشبكات</p>
        </div>
    </header>

    <!-- Learning Objectives -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold mb-6 text-center">
                    <i class="fas fa-bullseye text-cyan-600 ml-2"></i>
                    أهداف التعلم
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>فهم مبادئ جدران الحماية</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>تكوين أنظمة Sophos</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>إدارة السياسات الأمنية</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>مراقبة الشبكة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Firewall Simulation -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-6xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold mb-6 text-center">
                    <i class="fas fa-shield-alt text-blue-600 ml-2"></i>
                    محاكي جدار الحماية
                </h2>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="space-y-6">
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-xl font-bold mb-4">حالة الجدار</h3>
                            <div class="flex items-center justify-between">
                                <span class="text-lg">الحماية:</span>
                                <div class="flex items-center space-x-reverse space-x-2">
                                    <span id="firewall-status" class="text-red-500 font-bold">متوقف</span>
                                    <button id="firewall-toggle" onclick="toggleFirewall()" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                                        تشغيل الجدار
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-xl font-bold mb-4">إعدادات الحماية</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span>حماية الويب:</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" id="web-protection">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span>مكافحة الفيروسات:</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" id="antivirus">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span>مكافحة التطفل:</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" id="intrusion-prevention">
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-6">
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-xl font-bold mb-4">محاكاة الهجمات</h3>
                            <div class="space-y-4">
                                <button onclick="simulateAttack('malware')" class="w-full bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                                    <i class="fas fa-virus ml-2"></i>
                                    محاكاة هجوم فيروسي
                                </button>
                                <button onclick="simulateAttack('ddos')" class="w-full bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                                    <i class="fas fa-bomb ml-2"></i>
                                    محاكاة هجوم DDoS
                                </button>
                                <button onclick="simulateAttack('intrusion')" class="w-full bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 transition-colors">
                                    <i class="fas fa-user-ninja ml-2"></i>
                                    محاكاة محاولة اختراق
                                </button>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-xl font-bold mb-4">إحصائيات الحماية</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span>الهجمات المحجوبة:</span>
                                    <span id="blocked-attacks" class="font-bold text-green-600">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>الفيروسات المكتشفة:</span>
                                    <span id="detected-viruses" class="font-bold text-red-600">0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>الاتصالات الآمنة:</span>
                                    <span id="safe-connections" class="font-bold text-blue-600">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Security Dashboard -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-6xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold mb-6 text-center">
                    <i class="fas fa-tachometer-alt text-green-600 ml-2"></i>
                    لوحة الحماية
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-green-50 rounded-lg p-6 text-center">
                        <i class="fas fa-shield-alt text-green-600 text-4xl mb-4"></i>
                        <h3 class="text-xl font-bold mb-2">الحماية النشطة</h3>
                        <p class="text-green-600 font-bold text-2xl" id="active-protections">0</p>
                    </div>
                    <div class="bg-red-50 rounded-lg p-6 text-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-4xl mb-4"></i>
                        <h3 class="text-xl font-bold mb-2">التهديدات المكتشفة</h3>
                        <p class="text-red-600 font-bold text-2xl" id="detected-threats">0</p>
                    </div>
                    <div class="bg-blue-50 rounded-lg p-6 text-center">
                        <i class="fas fa-network-wired text-blue-600 text-4xl mb-4"></i>
                        <h3 class="text-xl font-bold mb-2">الشبكة الآمنة</h3>
                        <p class="text-blue-600 font-bold text-2xl" id="network-status">100%</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Network Scanner -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold mb-6 text-center">
                    <i class="fas fa-search text-purple-600 ml-2"></i>
                    ماسح الشبكة
                </h2>
                <div class="space-y-6">
                    <div class="flex justify-center">
                        <button id="scan-button" onclick="startNetworkScan()" class="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors">
                            <i class="fas fa-search ml-2"></i>
                            بدء الفحص
                        </button>
                    </div>
                    
                    <div id="scan-progress" class="hidden">
                        <div class="bg-gray-200 rounded-full h-4 mb-4">
                            <div id="progress-bar" class="bg-purple-500 h-4 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                        <div class="text-center">
                            <span id="scan-status">بدء الفحص...</span>
                        </div>
                    </div>
                    
                    <div id="scan-results" class="hidden">
                        <h3 class="text-xl font-bold mb-4">نتائج الفحص</h3>
                        <div id="scan-results-content" class="space-y-3">
                            <!-- Results will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Security Alerts -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold mb-6 text-center">
                    <i class="fas fa-bell text-orange-600 ml-2"></i>
                    تنبيهات الأمان
                </h2>
                <div id="security-alerts" class="space-y-4">
                    <div class="bg-gray-50 rounded-lg p-6 text-center text-gray-500">
                        <i class="fas fa-info-circle text-2xl mb-2"></i>
                        <p>لا توجد تنبيهات أمنية حالياً</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Configuration Panel -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-6xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold mb-6 text-center">
                    <i class="fas fa-cog text-gray-600 ml-2"></i>
                    إعدادات الجدار
                </h2>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="space-y-6">
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-xl font-bold mb-4">قواعد الحماية</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-white rounded border">
                                    <span>حجب المواقع الضارة</span>
                                    <i class="fas fa-check text-green-500"></i>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-white rounded border">
                                    <span>فحص الملفات المحملة</span>
                                    <i class="fas fa-check text-green-500"></i>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-white rounded border">
                                    <span>حماية البريد الإلكتروني</span>
                                    <i class="fas fa-check text-green-500"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-xl font-bold mb-4">إعدادات الشبكة</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <span>عنوان IP:</span>
                                    <span class="font-mono">***********</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span>قناع الشبكة:</span>
                                    <span class="font-mono">*************</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span>البوابة:</span>
                                    <span class="font-mono">*************</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-6">
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-xl font-bold mb-4">سجل الأحداث</h3>
                            <div id="event-log" class="space-y-2 max-h-64 overflow-y-auto">
                                <div class="text-sm text-gray-600 p-2 bg-white rounded">
                                    <span class="font-mono">[10:30:15]</span> تم تشغيل النظام
                                </div>
                                <div class="text-sm text-gray-600 p-2 bg-white rounded">
                                    <span class="font-mono">[10:30:20]</span> تم تحديث قواعد الحماية
                                </div>
                                <div class="text-sm text-gray-600 p-2 bg-white rounded">
                                    <span class="font-mono">[10:30:25]</span> تم بدء مراقبة الشبكة
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-xl font-bold mb-4">إجراءات سريعة</h3>
                            <div class="space-y-3">
                                <button onclick="updateRules()" class="w-full bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-sync ml-2"></i>
                                    تحديث القواعد
                                </button>
                                <button onclick="backupConfig()" class="w-full bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                                    <i class="fas fa-save ml-2"></i>
                                    حفظ الإعدادات
                                </button>
                                <button onclick="resetFirewall()" class="w-full bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                                    <i class="fas fa-undo ml-2"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Knowledge Quiz -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold mb-6 text-center">
                    <i class="fas fa-question-circle text-indigo-600 ml-2"></i>
                    اختبار المعرفة
                </h2>
                <div id="quiz-container">
                    <div id="question-container" class="mb-6">
                        <h3 id="question-title" class="text-xl font-bold mb-4"></h3>
                        <div id="answers-container" class="space-y-3">
                            <!-- Quiz answers will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <div>
                            <span id="question-counter" class="text-gray-600">السؤال 1 من 5</span>
                        </div>
                        <div class="space-x-reverse space-x-2">
                            <button id="prev-button" onclick="previousQuestion()" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors" disabled>
                                <i class="fas fa-chevron-right ml-1"></i>
                                السابق
                            </button>
                            <button id="next-button" onclick="nextQuestion()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                التالي
                                <i class="fas fa-chevron-left mr-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div id="quiz-results" class="hidden">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold mb-4">نتائج الاختبار</h3>
                        <div class="text-6xl mb-4" id="score-display">0/5</div>
                        <p class="text-gray-600 mb-6" id="score-message"></p>
                        <button onclick="restartQuiz()" class="bg-indigo-500 text-white px-6 py-3 rounded-lg hover:bg-indigo-600 transition-colors">
                            <i class="fas fa-redo ml-2"></i>
                            إعادة الاختبار
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2025 دورة الشبكات وأمن المعلومات</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/assets/js/main.js"></script>
    <script src="/assets/js/sophos-firewalls.js"></script>
</body>
</html>
