document.addEventListener("DOMContentLoaded", function() {
    // Mobile menu toggle
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function() {
            const menu = document.querySelector('.nav-link').parentElement;
            menu.classList.toggle('hidden');
        });
    }

    // Open specific module
    window.openModule = function(moduleId) {
        window.location.href = `pages/${moduleId}.html`;
    };

    // Start the course
    window.startCourse = function() {
        document.getElementById('modules').scrollIntoView({ behavior: 'smooth' });
    };

    // Module definitions - only core learning modules count for progress
    const coreModules = {
        'networks': 'networks', 
        'security': 'security',
        'alarm': 'alarm',
        'fire': 'fire',
        'video': 'video',
        'fire-protection': 'fireProtection'
    };
    
    // All modules including introduction for UI updates
    const allModules = {
        'introduction': 'introduction',
        ...coreModules
    };

    // Loading progress from local storage
    const progressBar = document.getElementById('progress-bar');
    const overallProgressText = document.getElementById('overall-progress');

    let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};

    function updateProgress() {
        // Only count core modules for progress calculation (excluding introduction)
        const totalModules = Object.keys(coreModules).length;
        const completedModules = Object.values(coreModules).filter(moduleKey => progress[moduleKey]).length;
        const percentage = Math.round((completedModules / totalModules) * 100);

        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
        if (overallProgressText) {
            overallProgressText.textContent = percentage + '%';
        }

        // Update individual module progress
        updateModuleCards();

        // Enable certificate if 100%
        const certificateBtn = document.getElementById('certificate-btn');
        if (certificateBtn && percentage === 100) {
            certificateBtn.textContent = 'احصل على شهادتك';
            certificateBtn.disabled = false;
            certificateBtn.classList.remove('cursor-not-allowed', 'bg-gray-400');
            certificateBtn.classList.add('bg-yellow-500', 'hover:bg-yellow-600', 'cursor-pointer');
            certificateBtn.onclick = function() {
                showCertificate();
            };
        }
    }

    function updateModuleCards() {
        Object.keys(allModules).forEach(moduleId => {
            const moduleCard = document.querySelector(`[data-module="${moduleId}"]`);
            if (moduleCard) {
                const progressText = moduleCard.querySelector('.module-progress-text');
                const button = moduleCard.querySelector('button');
                
                if (progress[allModules[moduleId]]) {
                    if (progressText) {
                        if (moduleId === 'introduction') {
                            progressText.textContent = 'مكتمل (غير محسوب)';
                        } else {
                            progressText.textContent = '100% مكتمل';
                        }
                    }
                    if (button) {
                        button.textContent = '✓ مكتمل';
                        button.classList.remove('bg-blue-500', 'bg-green-500', 'bg-red-500', 'bg-yellow-500', 'bg-purple-500', 'bg-red-600');
                        button.classList.add('bg-green-500', 'hover:bg-green-600');
                    }
                    moduleCard.classList.add('border-2', 'border-green-500');
                } else {
                    if (progressText) progressText.textContent = '0% مكتمل';
                    if (button) {
                        button.textContent = 'ابدأ';
                        button.classList.remove('bg-green-500', 'hover:bg-green-600');
                    }
                    moduleCard.classList.remove('border-2', 'border-green-500');
                }
            }
        });
    }

    function showCertificate() {
        // Create certificate modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white p-8 rounded-2xl shadow-2xl max-w-md mx-4 text-center">
                <i class="fas fa-certificate text-6xl text-yellow-500 mb-4"></i>
                <h2 class="text-2xl font-bold mb-4">تهانينا!</h2>
                <p class="mb-6">لقد أكملت دورة الشبكات وأمن المعلومات بنجاح</p>
                <div class="mb-6">
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg">
                        <p class="font-bold">شهادة إتمام الدورة</p>
                        <p class="text-sm">الشبكات وأمن المعلومات</p>
                        <p class="text-xs mt-2">التاريخ: ${new Date().toLocaleDateString('ar-SA')}</p>
                    </div>
                </div>
                <button onclick="this.closest('.fixed').remove()" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600">
                    إغلاق
                </button>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Initialize progress
    updateProgress();

    // Check for any returned progress updates
    window.addEventListener('storage', function(e) {
        if (e.key === 'courseProgress') {
            progress = JSON.parse(e.newValue) || {};
            updateProgress();
        }
    });

    // Refresh progress when page becomes visible (when returning from a module)
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
            updateProgress();
        }
    });

    // Refresh progress when window gains focus
    window.addEventListener('focus', function() {
        progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
        updateProgress();
    });

    // Test function for progress tracking (development only)
    window.testProgress = function() {
        // Simulate completing some core modules (introduction doesn't count)
        let testProgress = {
            introduction: true,  // This won't count towards progress
            video: true,         // This counts (1/6)
            fireProtection: true // This counts (2/6)
        };
        localStorage.setItem('courseProgress', JSON.stringify(testProgress));
        progress = testProgress;
        updateProgress();
        alert('تم تعيين تقدم تجريبي: 2 من 6 وحدات أساسية مكتملة (33%)');
    };

    // Reset progress function (development only)
    window.resetProgress = function() {
        localStorage.removeItem('courseProgress');
        progress = {};
        updateProgress();
        alert('تم إعادة تعيين التقدم');
    };
});
