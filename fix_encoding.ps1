# PowerShell script to fix UTF-8 encoding in all HTML files

Write-Host "Fixing UTF-8 encoding in HTML files..." -ForegroundColor Green

# Get all HTML files
$htmlFiles = Get-ChildItem -Path "D:\AI Code App\Networking-Course\src" -Filter "*.html" -Recurse

foreach ($file in $htmlFiles) {
    Write-Host "Processing: $($file.Name)" -ForegroundColor Yellow
    
    # Read file content
    $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
    
    # Check if it already has the Content-Type meta tag
    if ($content -notmatch '<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">') {
        # Add the Content-Type meta tag after the charset meta tag
        $content = $content -replace '(<meta charset="UTF-8">)', "`$1`r`n    <meta http-equiv=`"Content-Type`" content=`"text/html; charset=UTF-8`">"
        
        # Write the content back to the file with UTF-8 encoding
        [System.IO.File]::WriteAllText($file.FullName, $content, [System.Text.Encoding]::UTF8)
        
        Write-Host "✓ Fixed: $($file.Name)" -ForegroundColor Green
    } else {
        Write-Host "✓ Already fixed: $($file.Name)" -ForegroundColor Cyan
    }
}

Write-Host "All HTML files have been processed!" -ForegroundColor Green
