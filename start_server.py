#!/usr/bin/env python3
"""
Simple HTTP Server for testing the Networking Course website
"""
import http.server
import socketserver
import webbrowser
import os
import sys

# Change to the src directory
os.chdir('src')

PORT = 8000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

def start_server():
    handler = MyHTTPRequestHandler
    
    with socketserver.TCPServer(("", PORT), handler) as httpd:
        print(f"🚀 Starting server at http://localhost:{PORT}")
        print(f"📁 Serving files from: {os.getcwd()}")
        print("🔗 Opening browser...")
        print("⏹️  Press Ctrl+C to stop the server")
        
        # Open browser automatically
        webbrowser.open(f'http://localhost:{PORT}')
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped by user")
            sys.exit(0)

if __name__ == "__main__":
    start_server()
