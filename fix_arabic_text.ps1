# Comprehensive Arabic text fix script for all HTML files

Write-Host "Fixing Arabic text encoding in all HTML files..." -ForegroundColor Green

# Define mappings for corrupted text to proper Arabic
$textMappings = @{
    # Introduction text fixes
    "Ù…Ù‚Ø¯Ù…Ø© Ø§Ù„Ø¯ÙˆØ±Ø©" = "مقدمة الدورة"
    "Ø§Ù„Ø±Ø¦ÙŠØ³ÙŠØ©" = "الرئيسية"
    "Ø¥ÙƒÙ…Ø§Ù„ Ø§Ù„ÙˆØ­Ø¯Ø©" = "إكمال الوحدة"
    "Ø¯ÙˆØ±Ø© ØªØ¯Ø±ÙŠØ¨ÙŠØ© Ù…ØªÙƒØ§Ù…Ù„Ø©" = "دورة تدريبية متكاملة"
    "Ø§Ù„Ø´Ø¨ÙƒØ§Øª ÙˆØ£Ù…Ù† Ø§Ù„Ù…Ø¹Ù„ÙˆÙ…Ø§Øª Ù„Ù„Ù…Ø¨ØªØ¯Ø¦ÙŠÙ†" = "الشبكات وأمن المعلومات للمبتدئين"
    "Ù„Ù…Ø§Ø°Ø§ Ù‡Ø°Ù‡ Ø§Ù„Ù…Ù‡Ø§Ø±Ø§Øª Ù…Ù‡Ù…Ø©ØŸ" = "لماذا هذه المهارات مهمة؟"
    "Ø£Ù‡Ø¯Ø§Ù Ø§Ù„ØªØ¹Ù„Ù…" = "أهداف التعلم"
    
    # Fire protection text fixes
    "Ø§Ù„Ø­Ù…Ø§ÙŠØ© Ù…Ù† Ø§Ù„Ø­Ø±Ø§Ø¦Ù‚ - Ø¯ÙˆØ±Ø© Ø§Ù„Ø´Ø¨ÙƒØ§Øª" = "الحماية من الحرائق - دورة الشبكات"
    "Ø§Ù„Ø­Ù…Ø§ÙŠØ© Ù…Ù† Ø§Ù„Ø­Ø±Ø§Ø¦Ù‚" = "الحماية من الحرائق"
    "Ø£Ù†Ø¸Ù…Ø© Ù…ÙƒØ§ÙØ­Ø© Ø§Ù„Ø­Ø±Ø§Ø¦Ù‚ ÙˆØ§Ù„ÙˆÙ‚Ø§ÙŠØ© Ù…Ù† Ø§Ù„Ø£Ø®Ø·Ø§Ø±" = "أنظمة مكافحة الحرائق والوقاية من الأخطار"
    "Ø£Ù†ÙˆØ§Ø¹ Ø§Ù„Ø­Ø±Ø§Ø¦Ù‚" = "أنواع الحرائق"
    "Ø§Ù„Ù…ÙˆØ§Ø¯ Ø§Ù„ØµÙ„Ø¨Ø©" = "المواد الصلبة"
    "Ø§Ù„Ø³ÙˆØ§Ø¦Ù„ Ø§Ù„Ù…Ø´ØªØ¹Ù„Ø©" = "السوائل المشتعلة"
    "Ø§Ù„Ø£Ø¬Ù‡Ø²Ø© Ø§Ù„ÙƒÙ‡Ø±Ø¨Ø§Ø¦ÙŠØ©" = "الأجهزة الكهربائية"
    
    # Video surveillance text fixes
    "Ø§Ù„Ù…Ø±Ø§Ù‚Ø¨Ø© Ø¨Ø§Ù„ÙÙŠØ¯ÙŠÙˆ - Ø¯ÙˆØ±Ø© Ø§Ù„Ø´Ø¨ÙƒØ§Øª" = "المراقبة بالفيديو - دورة الشبكات"
    "Ø§Ù„Ù…Ø±Ø§Ù‚Ø¨Ø© Ø¨Ø§Ù„ÙÙŠØ¯ÙŠÙˆ" = "المراقبة بالفيديو"
    "Ù†Ø¸Ø§Ù… Ø§Ù„Ù…Ø±Ø§Ù‚Ø¨Ø© Ø§Ù„Ù…ØªÙ‚Ø¯Ù…Ø©" = "نظام المراقبة المتقدمة"
    "ØªØ¯Ø±ÙŠØ¨ Ø´Ø§Ù…Ù„ Ø¹Ù„Ù‰ Ø£Ù†Ø¸Ù…Ø© Ø§Ù„Ù…Ø±Ø§Ù‚Ø¨Ø© Ø¨Ø§Ù„ÙÙŠØ¯ÙŠÙˆ ÙˆØªÙ‚Ù†ÙŠØ§ØªÙ‡Ø§" = "تدريب شامل على أنظمة المراقبة بالفيديو وتقنياتها"
    "Ù…Ø­Ø§ÙƒØ§Ø© ÙƒØ§Ù…ÙŠØ±Ø§ Ù…Ø±Ø§Ù‚Ø¨Ø©" = "محاكاة كاميرا مراقبة"
    "Ø¨Ø¯Ø¡ Ø§Ù„Ù…Ø­Ø§ÙƒØ§Ø©" = "بدء المحاكاة"
    "Ø¥Ø¹Ø§Ø¯Ø© ØªØ¹ÙŠÙŠÙ†" = "إعادة تعيين"
    
    # Common interface text
    "ØªÙ†ÙÙŠØ°" = "تنفيذ"
    "Ø§Ø®ØªÙŠØ§Ø± Ø§Ù„Ù…ÙˆÙ‚Ø¹" = "اختيار الموقع"
    "ØªØ±ÙƒÙŠØ¨ Ø§Ù„ÙƒØ§Ù…ÙŠØ±Ø§" = "تركيب الكاميرا"
    "ØªÙˆØµÙŠÙ„ Ø§Ù„ÙƒØ§Ù…ÙŠØ±Ø§" = "توصيل الكاميرا"
    "ØªÙƒÙˆÙŠÙ† Ø§Ù„Ù†Ø¸Ø§Ù…" = "تكوين النظام"
    
    # Fire protection systems
    "Ø£Ù†Ø¸Ù…Ø© Ø§Ù„Ø­Ù…Ø§ÙŠØ© Ù…Ù† Ø§Ù„Ø­Ø±Ø§Ø¦Ù‚ - Ø¯ÙˆØ±Ø© Ø§Ù„Ø´Ø¨ÙƒØ§Øª" = "أنظمة الحماية من الحرائق - دورة الشبكات"
    "Ø£Ù†Ø¸Ù…Ø© Ø§Ù„Ø­Ù…Ø§ÙŠØ© Ù…Ù† Ø§Ù„Ø­Ø±Ø§Ø¦Ù‚" = "أنظمة الحماية من الحرائق"
    
    # Sophos Firewalls
    "Ø¬Ø¯Ø±Ø§Ù† Ø­Ù…Ø§ÙŠØ© Sophos - Ø¯ÙˆØ±Ø© Ø§Ù„Ø´Ø¨ÙƒØ§Øª" = "جدران حماية Sophos - دورة الشبكات"
    "Ø¬Ø¯Ø±Ø§Ù† Ø­Ù…Ø§ÙŠØ© Sophos" = "جدران حماية Sophos"
}

# Function to fix text in a file
function Fix-ArabicTextInFile {
    param (
        [string]$FilePath
    )
    
    Write-Host "Processing: $([System.IO.Path]::GetFileName($FilePath))" -ForegroundColor Yellow
    
    # Read file content with UTF-8 encoding
    $content = [System.IO.File]::ReadAllText($FilePath, [System.Text.Encoding]::UTF8)
    
    $modified = $false
    
    # Apply text mappings
    foreach ($mapping in $textMappings.GetEnumerator()) {
        if ($content -match [regex]::Escape($mapping.Key)) {
            $content = $content -replace [regex]::Escape($mapping.Key), $mapping.Value
            $modified = $true
            Write-Host "  Fixed: $($mapping.Key) -> $($mapping.Value)" -ForegroundColor Cyan
        }
    }
    
    if ($modified) {
        # Write back with UTF-8 encoding
        [System.IO.File]::WriteAllText($FilePath, $content, [System.Text.Encoding]::UTF8)
        Write-Host "  ✓ File updated successfully" -ForegroundColor Green
    } else {
        Write-Host "  ✓ No changes needed" -ForegroundColor Gray
    }
}

# Get all HTML files in the pages directory
$htmlFiles = Get-ChildItem -Path "D:\AI Code App\Networking-Course\src\pages" -Filter "*.html"

foreach ($file in $htmlFiles) {
    Fix-ArabicTextInFile -FilePath $file.FullName
}

Write-Host ""
Write-Host "Arabic text fix completed for all HTML files!" -ForegroundColor Green
Write-Host "The following files have been processed:" -ForegroundColor Cyan
$htmlFiles | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor White }
