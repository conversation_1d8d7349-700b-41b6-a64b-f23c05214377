// Fire Protection Quiz Variables
let fireQuizScore = 0;
let fireAnsweredQuestions = 0;

// Quiz Functions
function selectFireAnswer(element, isCorrect) {
    const questionDiv = element.closest('.quiz-question');
    const allOptions = questionDiv.querySelectorAll('.quiz-option');

    allOptions.forEach(option => {
        option.style.pointerEvents = 'none';
        option.classList.add('opacity-60');
    });

    if (isCorrect) {
        element.classList.add('border-green-500', 'bg-green-100');
        fireQuizScore++;
    } else {
        element.classList.add('border-red-500', 'bg-red-100');
    }
    
    fireAnsweredQuestions++;
    if (fireAnsweredQuestions >= 2) {
        showFireQuizResults();
    }
}

function showFireQuizResults() {
    const resultsDiv = document.getElementById('fire-quiz-results');
    const scoreSpan = document.getElementById('fire-quiz-score');
    
    scoreSpan.textContent = fireQuizScore;
    resultsDiv.classList.remove('hidden');
    resultsDiv.scrollIntoView({ behavior: 'smooth' });
}

// Module completion function
function completeModule() {
    let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
    progress.fireProtection = true;
    localStorage.setItem('courseProgress', JSON.stringify(progress));
    showNotification('تم إكمال وحدة أنظمة الحماية من الحرائق بنجاح!', 'success');
    setTimeout(() => {
        window.location.href = '../index.html';
    }, 1500);
}

// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-5 right-5 p-4 rounded-lg shadow-lg z-50 text-white animate-fade-in`;
    const icons = {
        info: 'fa-info-circle',
        success: 'fa-check-circle',
        error: 'fa-exclamation-triangle'
    };
    const colors = {
        info: 'bg-blue-500',
        success: 'bg-green-500',
        error: 'bg-red-500'
    };
    notification.classList.add(colors[type]);
    notification.innerHTML = `<i class="fas ${icons[type]} ml-2"></i> ${message}`;
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.classList.add('animate-fade-out');
        setTimeout(() => notification.remove(), 500);
    }, 3000);
}

document.addEventListener('DOMContentLoaded', () => {
    // Component info panels
    const componentCards = document.querySelectorAll('.component-card');
    componentCards.forEach(card => {
        card.addEventListener('click', () => {
            const infoId = card.getAttribute('onclick').match(/'([^']*)'/)[1];
            showComponentInfo(infoId);
        });
    });

    // FAQ accordion
    const faqQuestions = document.querySelectorAll('.faq-question');
    faqQuestions.forEach(question => {
        question.addEventListener('click', () => {
            const item = question.parentElement;
            item.classList.toggle('active');
            const answer = question.nextElementSibling;
            if (item.classList.contains('active')) {
                answer.style.display = 'block';
            } else {
                answer.style.display = 'none';
            }
        });
    });
});

function showComponentInfo(infoId) {
    const allInfoPanels = document.querySelectorAll('.info-panel');
    allInfoPanels.forEach(panel => {
        panel.classList.add('hidden');
    });

    const selectedInfoPanel = document.getElementById(infoId + '-info');
    if (selectedInfoPanel) {
        selectedInfoPanel.classList.remove('hidden');
    }
}

