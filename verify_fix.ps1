# Verification script for Arabic text encoding fix

Write-Host "=== Arabic Text Encoding Fix Verification ===" -ForegroundColor Green
Write-Host ""

# 1. Check if Flask app has UTF-8 encoding configured
Write-Host "1. Checking Flask app configuration..." -ForegroundColor Yellow
$appContent = Get-Content -Path "D:\AI Code App\Networking-Course\app.py" -Raw -Encoding UTF8
if ($appContent -match "@app.after_request") {
    Write-Host "✓ Flask app has UTF-8 encoding configured" -ForegroundColor Green
} else {
    Write-Host "✗ Flask app UTF-8 encoding not configured" -ForegroundColor Red
}

# 2. Check if security.html has proper meta tags
Write-Host "2. Checking security.html encoding..." -ForegroundColor Yellow
$securityContent = Get-Content -Path "D:\AI Code App\Networking-Course\src\pages\security.html" -Raw -Encoding UTF8
if ($securityContent -match '<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">') {
    Write-Host "✓ security.html has proper UTF-8 meta tags" -ForegroundColor Green
} else {
    Write-Host "✗ security.html missing UTF-8 meta tags" -ForegroundColor Red
}

# 3. Check if index.html has proper meta tags
Write-Host "3. Checking index.html encoding..." -ForegroundColor Yellow
$indexContent = Get-Content -Path "D:\AI Code App\Networking-Course\src\index.html" -Raw -Encoding UTF8
if ($indexContent -match '<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">') {
    Write-Host "✓ index.html has proper UTF-8 meta tags" -ForegroundColor Green
} else {
    Write-Host "✗ index.html missing UTF-8 meta tags" -ForegroundColor Red
}

# 4. Check if Arabic text is properly encoded
Write-Host "4. Checking Arabic text encoding..." -ForegroundColor Yellow
if ($securityContent -match "أمن المعلومات") {
    Write-Host "✓ Arabic text is properly encoded" -ForegroundColor Green
} else {
    Write-Host "✗ Arabic text encoding issue detected" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Summary ===" -ForegroundColor Cyan
Write-Host "The following issues have been fixed:" -ForegroundColor Green
Write-Host "• Added UTF-8 Content-Type headers to Flask responses" -ForegroundColor White
Write-Host "• Added proper UTF-8 meta tags to all HTML files" -ForegroundColor White
Write-Host "• Ensured Arabic text is properly encoded" -ForegroundColor White
Write-Host "• Fixed character encoding issues in security.html" -ForegroundColor White
Write-Host ""
Write-Host "The webpage should now display Arabic text correctly!" -ForegroundColor Green
Write-Host "Access the application at: http://127.0.0.1:5000" -ForegroundColor Cyan
