// Video Surveillance Page Functionality

// --- Camera Info Panel ---
const cameraInfo = {
    'ip': {
        title: 'كاميرات IP - معلومات تفصيلية',
        content: `
            <p>كاميرات رقمية تتصل بالشبكة مباشرة، وتوفر جودة صورة عالية وميزات متقدمة مثل التحليل الذكي والوصول عن بعد.</p>
            <ul class="list-disc list-inside mt-2">
                <li><strong>الجودة:</strong> عالية جداً (HD, 4K).</li>
                <li><strong>المرونة:</strong> يمكن توصيلها عبر Wi-Fi أو كابلات الشبكة.</li>
                <li><strong>التكلفة:</strong> أعلى من الكاميرات التناظرية.</li>
            </ul>`
    },
    'ptz': {
        title: 'كاميرات PTZ - معلومات تفصيلية',
        content: `<p>كاميرات متحركة (Pan-Tilt-Zoom) يمكن التحكم بها عن بعد لتدويرها وإمالتها وتقريب الصورة، مما يجعلها مثالية لتغطية مساحات واسعة.</p>
            <ul class="list-disc list-inside mt-2">
                <li><strong>التحكم:</strong> كامل في الحركة والتقريب.</li>
                <li><strong>التغطية:</strong> واسعة جداً، تغني عن عدة كاميرات ثابتة.</li>
                <li><strong>الاستخدام:</strong> مثالية للمراقبة الحية في المواقع الكبيرة.</li>
            </ul>`
    },
    'thermal': {
        title: 'الكاميرات الحرارية - معلومات تفصيلية',
        content: `<p>تعتمد على استشعار الحرارة المنبعثة من الأجسام بدلاً من الضوء المرئي. تعمل في الظلام الدامس والظروف الجوية السيئة.</p>
            <ul class="list-disc list-inside mt-2">
                <li><strong>الرؤية:</strong> تعمل في جميع الظروف الإضائية.</li>
                <li><strong>الكشف:</strong> فعالة في كشف المتسللين والحرائق.</li>
                <li><strong>التكلفة:</strong> مرتفعة جداً.</li>
            </ul>`
    },
    'ai': {
        title: 'الكاميرات الذكية (AI) - معلومات تفصيلية',
        content: `<p>كاميرات مزودة بمعالجات لتحليل الفيديو مباشرة على الكاميرا. يمكنها التعرف على الأشخاص، المركبات، السلوكيات المحددة، وتقليل الإنذارات الكاذبة.</p>
            <ul class="list-disc list-inside mt-2">
                <li><strong>التحليل:</strong> ذكي ومدمج.</li>
                <li><strong>الدقة:</strong> تنبيهات دقيقة وموثوقة.</li>
                <li><strong>التطبيقات:</strong> التعرف على الوجوه، تتبع الحركة، إحصاء عدد الأشخاص.</li>
            </ul>`
    }
};

function showCameraInfo(type) {
    const panel = document.getElementById('camera-info-panel');
    const title = document.getElementById('camera-info-title');
    const content = document.getElementById('camera-info-content');

    if (cameraInfo[type]) {
        title.innerHTML = `<i class="fas fa-info-circle text-purple-600 ml-2"></i> ${cameraInfo[type].title}`;
        content.innerHTML = cameraInfo[type].content;
        panel.classList.remove('hidden');
        panel.scrollIntoView({ behavior: 'smooth' });
    }
}

// --- Camera Setup Simulator ---
let currentStep = 0;
const setupSteps = ['location', 'mount', 'connect', 'configure'];
const totalSetupSteps = setupSteps.length;

function startSimulation() {
    document.getElementById('start-simulation').disabled = true;
    document.getElementById('start-simulation').classList.add('opacity-50');
    document.getElementById('camera-model').classList.remove('hidden');
    
    resetSimulationState();
    enableStepButton(0);
    updateProgress();
}

function executeSetup(stepName) {
    const stepIndex = setupSteps.indexOf(stepName);
    if (stepIndex !== currentStep) return;

    const stepDiv = document.getElementById(`step-${stepName}`);
    stepDiv.classList.add('border-green-500');
    
    const btn = document.getElementById(`${stepName}-btn`);
    btn.disabled = true;
    btn.innerHTML = `<i class="fas fa-check ml-2"></i> تم التنفيذ`;

    currentStep++;
    updateProgress();

    if (currentStep < totalSetupSteps) {
        enableStepButton(currentStep);
    } else {
        document.getElementById('success-message').classList.remove('hidden');
    }
}

function enableStepButton(stepIndex) {
    const stepName = setupSteps[stepIndex];
    const btn = document.getElementById(`${stepName}-btn`);
    btn.disabled = false;
}

function resetSimulation() {
    document.getElementById('start-simulation').disabled = false;
    document.getElementById('start-simulation').classList.remove('opacity-50');
    document.getElementById('camera-model').classList.add('hidden');
    
    resetSimulationState();
    updateProgress();
}

function resetSimulationState() {
    currentStep = 0;
    document.getElementById('success-message').classList.add('hidden');
    setupSteps.forEach((step, index) => {
        const stepDiv = document.getElementById(`step-${step}`);
        stepDiv.classList.remove('border-green-500');
        const btn = document.getElementById(`${step}-btn`);
        btn.disabled = true;
        btn.innerHTML = 'تنفيذ';
    });
}

function updateProgress() {
    const progress = (currentStep / totalSetupSteps) * 100;
    document.getElementById('setup-progress').style.width = `${progress}%`;
    const progressText = document.getElementById('progress-text');
    const progressPercentage = document.getElementById('progress-percentage');
    
    if (progressPercentage) {
        progressPercentage.textContent = `${Math.round(progress)}%`;
    }
    
    if (currentStep === 0) {
        progressText.textContent = 'انقر على "بدء المحاكاة" لبدء العمل';
    } else if (currentStep === totalSetupSteps) {
        progressText.textContent = 'اكتمل الإعداد بنجاح!';
    } else {
        progressText.textContent = `التقدم: ${currentStep} من ${totalSetupSteps} خطوات مكتملة.`;
    }
}

// --- Quiz ---
const quizQuestions = [
    {
        question: "أي نوع من الكاميرات يوفر القدرة على الدوران والتقريب عن بعد؟",
        options: ["كاميرات IP", "كاميرات PTZ", "كاميرات حرارية", "كاميرات ذكية"],
        answer: "كاميرات PTZ"
    },
    {
        question: "ما هي الفائدة الرئيسية للكاميرات الحرارية؟",
        options: ["جودة صورة عالية", "العمل في الظلام الدامس", "سهولة التركيب", "التكلفة المنخفضة"],
        answer: "العمل في الظلام الدامس"
    },
    {
        question: "ماذا يعني مصطلح 'AI' في الكاميرات الذكية؟",
        options: ["وصول للإنترنت", "جودة صورة محسنة", "تحليل فيديو مدمج", "قدرة على مقاومة الماء"],
        answer: "تحليل فيديو مدمج"
    }
];
let currentQuizQuestion = 0;
let score = 0;

function loadQuiz() {
    const quizContainer = document.getElementById('quiz-container');
    if (!quizContainer) return;
    quizContainer.innerHTML = '';
    const q = quizQuestions[currentQuizQuestion];
    
    const questionDiv = document.createElement('div');
    questionDiv.className = 'quiz-question';
    questionDiv.innerHTML = `<h3 class="text-xl font-semibold mb-4">${currentQuizQuestion + 1}. ${q.question}</h3>`;
    
    const optionsDiv = document.createElement('div');
    optionsDiv.className = 'space-y-3';
    
    q.options.forEach(option => {
        const optionBtn = document.createElement('button');
        optionBtn.className = 'quiz-option p-4 w-full text-right border-2 border-gray-200 rounded-lg cursor-pointer hover:border-purple-500 transition-colors';
        optionBtn.textContent = option;
        optionBtn.onclick = () => selectQuizAnswer(optionBtn, option === q.answer);
        optionsDiv.appendChild(optionBtn);
    });
    
    questionDiv.appendChild(optionsDiv);
    quizContainer.appendChild(questionDiv);
}

function selectQuizAnswer(btn, isCorrect) {
    const questionDiv = btn.parentElement;
    Array.from(questionDiv.children).forEach(child => child.disabled = true);
    
    if (isCorrect) {
        score++;
        btn.classList.add('bg-green-100', 'border-green-500');
    } else {
        btn.classList.add('bg-red-100', 'border-red-500');
    }
    
    setTimeout(() => {
        currentQuizQuestion++;
        if (currentQuizQuestion < quizQuestions.length) {
            loadQuiz();
        } else {
            showQuizResults();
        }
    }, 1000);
}

function showQuizResults() {
    const quizContainer = document.getElementById('quiz-container');
    if (!quizContainer) return;
    
    quizContainer.innerHTML = `
        <div class="text-center bg-blue-50 border border-blue-200 rounded-lg p-6">
            <i class="fas fa-trophy text-yellow-500 text-4xl mb-4"></i>
            <h3 class="text-2xl font-bold text-blue-800 mb-2">نتائج الاختبار</h3>
            <p class="text-blue-700 mb-4">لقد أجبت على ${score} من ${quizQuestions.length} أسئلة بشكل صحيح</p>
            <div class="text-3xl font-bold text-blue-600 mb-4">${Math.round((score / quizQuestions.length) * 100)}%</div>
            <button onclick="resetQuiz()" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-redo ml-2"></i>
                إعادة الاختبار
            </button>
        </div>
    `;
}

// --- General Functions ---
function completeModule() {
    let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
    progress.video = true;
    localStorage.setItem('courseProgress', JSON.stringify(progress));
    showNotification('تم إكمال وحدة المراقبة بالفيديو بنجاح!', 'success');
    setTimeout(() => {
        window.location.href = '../index.html';
    }, 1500);
}

// Video controls
const videoElement = document.getElementById('main-video');
const playPauseButton = document.getElementById('play-pause');
const muteButton = document.getElementById('mute-toggle');
const speedButton = document.getElementById('speed-toggle');
const fullscreenButton = document.getElementById('fullscreen-toggle');
const progressBar = document.getElementById('progress-bar');
const progressFill = document.getElementById('progress-fill');
const timeDisplay = document.getElementById('time-display');

if (playPauseButton && videoElement) {
    playPauseButton.addEventListener('click', () => {
        if (videoElement.paused) {
            videoElement.play();
            playPauseButton.innerHTML = '<i class="fas fa-pause"></i>';
        } else {
            videoElement.pause();
            playPauseButton.innerHTML = '<i class="fas fa-play"></i>';
        }
    });
}

if (muteButton && videoElement) {
    muteButton.addEventListener('click', () => {
        videoElement.muted = !videoElement.muted;
        muteButton.innerHTML = videoElement.muted ? '<i class="fas fa-volume-mute"></i>' : '<i class="fas fa-volume-up"></i>';
    });
}

if (speedButton && videoElement) {
    speedButton.addEventListener('click', () => {
        videoElement.playbackRate = videoElement.playbackRate === 1 ? 1.5 : 1;
        speedButton.innerHTML = `<i class="fas fa-tachometer-alt"></i> ${videoElement.playbackRate}x`;
    });
}

if (fullscreenButton && videoElement) {
    fullscreenButton.addEventListener('click', () => {
        if (!document.fullscreenElement) {
            if (videoElement.requestFullscreen) {
                videoElement.requestFullscreen();
            } else if (videoElement.mozRequestFullScreen) {
                videoElement.mozRequestFullScreen();
            } else if (videoElement.webkitRequestFullscreen) {
                videoElement.webkitRequestFullscreen();
            } else if (videoElement.msRequestFullscreen) {
                videoElement.msRequestFullscreen();
            }
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.mozCancelFullScreen) {
                document.mozCancelFullScreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    });
}

if (videoElement && progressFill && timeDisplay) {
    videoElement.addEventListener('timeupdate', () => {
        const progress = (videoElement.currentTime / videoElement.duration) * 100;
        progressFill.style.width = `${progress}%`;
        const minutes = Math.floor(videoElement.currentTime / 60);
        const seconds = Math.floor(videoElement.currentTime % 60).toString().padStart(2, '0');
        const durationMinutes = Math.floor(videoElement.duration / 60);
        const durationSeconds = Math.floor(videoElement.duration % 60).toString().padStart(2, '0');
        timeDisplay.textContent = `${minutes}:${seconds} / ${durationMinutes}:${durationSeconds}`;
    });
}

// Reset quiz function
function resetQuiz() {
    currentQuizQuestion = 0;
    score = 0;
    loadQuiz();
}

// Feedback form
function toggleFeedbackForm() {
    const feedbackForm = document.getElementById('feedback-form');
    if (feedbackForm) {
        feedbackForm.classList.toggle('show');
    }
}

let currentRating = 0;
function rate(stars) {
    currentRating = stars;
    const starElements = document.querySelectorAll('.rating-star');
    starElements.forEach((star, index) => {
        star.classList.toggle('active', index < currentRating);
    });
}

function submitFeedback() {
    const feedbackText = document.getElementById('feedback-text').value;
    if (currentRating === 0 || !feedbackText) {
        showNotification('يرجى تقديم تقييم وكتابة ملاحظات.', 'error');
        return;
    }
    showNotification('شكراً لتعليقاتك!', 'success');
    toggleFeedbackForm();
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-5 right-5 p-4 rounded-lg shadow-lg z-50 text-white animate-fade-in`;
    const icons = {
        info: 'fa-info-circle',
        success: 'fa-check-circle',
        error: 'fa-exclamation-triangle'
    };
    const colors = {
        info: 'bg-blue-500',
        success: 'bg-green-500',
        error: 'bg-red-500'
    };
    notification.classList.add(colors[type]);
    notification.innerHTML = `<i class="fas ${icons[type]} ml-2"></i> ${message}`;
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.classList.add('animate-fade-out');
        setTimeout(() => notification.remove(), 500);
    }, 3000);
}

// Chat functionality
function sendChatMessage() {
    const chatInput = document.getElementById('chat-input');
    const message = chatInput.value.trim();
    if (!message) return;
    
    const chatContainer = document.getElementById('chat-container');
    const userMessage = document.createElement('div');
    userMessage.className = 'chat-message user';
    userMessage.textContent = message;
    chatContainer.appendChild(userMessage);
    
    chatInput.value = '';
    
    // Simple auto-response
    setTimeout(() => {
        const systemMessage = document.createElement('div');
        systemMessage.className = 'chat-message system';
        systemMessage.textContent = 'شكراً لسؤالك! سيتم الرد عليه قريباً.';
        chatContainer.appendChild(systemMessage);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }, 1000);
    
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

// Interactive diagram
const diagramInfo = {
    'router': {
        title: 'الراوتر',
        content: 'يوجه البيانات بين الشبكات المختلفة ويوفر الاتصال بالإنترنت لجميع الأجهزة في النظام.'
    },
    'nvr': {
        title: 'مسجل الشبكة (NVR)',
        content: 'يستقبل ويسجل الفيديو من الكاميرات الشبكية، ويوفر إمكانية التشغيل والبحث في التسجيلات.'
    },
    'switch': {
        title: 'محول الشبكة (Switch)',
        content: 'يربط عدة أجهزة في الشبكة المحلية ويوفر منافذ إضافية لتوصيل الكاميرات.'
    },
    'camera1': {
        title: 'كاميرا المراقبة 1',
        content: 'كاميرا IP تلتقط الفيديو وترسله عبر الشبكة إلى جهاز التسجيل.'
    },
    'camera2': {
        title: 'كاميرا المراقبة 2',
        content: 'كاميرا IP ثانية تغطي زاوية مختلفة من المنطقة المراقبة.'
    }
};

function showDiagramInfo(element) {
    const infoDiv = document.getElementById('diagram-info');
    const titleDiv = document.getElementById('diagram-info-title');
    const contentDiv = document.getElementById('diagram-info-content');
    
    if (diagramInfo[element]) {
        titleDiv.textContent = diagramInfo[element].title;
        contentDiv.textContent = diagramInfo[element].content;
        infoDiv.classList.remove('hidden');
    }
}

// Drag and drop game
const gameCorrectOrder = ['camera-support', 'camera-install', 'cables', 'nvr', 'software', 'testing'];
let gameCurrentAnswers = [];
let draggedElement = null;

// Initialize drag and drop functionality
function initializeDragAndDrop() {
    // Add drag event listeners to drag items
    const dragItems = document.querySelectorAll('.drag-item');
    dragItems.forEach(item => {
        item.addEventListener('dragstart', handleDragStart);
        item.addEventListener('dragend', handleDragEnd);
    });
    
    // Add drop event listeners to drop zones
    const dropZones = document.querySelectorAll('.drop-zone');
    dropZones.forEach(zone => {
        zone.addEventListener('dragover', handleDragOver);
        zone.addEventListener('dragenter', handleDragEnter);
        zone.addEventListener('dragleave', handleDragLeave);
        zone.addEventListener('drop', handleDrop);
    });
}

function handleDragStart(e) {
    draggedElement = e.target;
    e.dataTransfer.setData('text/plain', e.target.dataset.component);
    e.dataTransfer.effectAllowed = 'move';
    e.target.style.opacity = '0.5';
}

function handleDragEnd(e) {
    e.target.style.opacity = '1';
    draggedElement = null;
}

function handleDragOver(e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
}

function handleDragEnter(e) {
    e.preventDefault();
    
    // Find the drop zone (could be the target or a parent)
    let dropZone = e.target;
    while (dropZone && !dropZone.classList.contains('drop-zone')) {
        dropZone = dropZone.parentElement;
    }
    
    if (dropZone && dropZone.classList.contains('drop-zone')) {
        dropZone.classList.add('drag-over');
    }
}

function handleDragLeave(e) {
    // Find the drop zone (could be the target or a parent)
    let dropZone = e.target;
    while (dropZone && !dropZone.classList.contains('drop-zone')) {
        dropZone = dropZone.parentElement;
    }
    
    if (dropZone && dropZone.classList.contains('drop-zone')) {
        dropZone.classList.remove('drag-over');
    }
}

function handleDrop(e) {
    e.preventDefault();
    
    // Find the drop zone (could be the target or a parent)
    let dropZone = e.target;
    while (dropZone && !dropZone.classList.contains('drop-zone')) {
        dropZone = dropZone.parentElement;
    }
    
    if (dropZone && dropZone.classList.contains('drop-zone')) {
        const component = e.dataTransfer.getData('text/plain');
        const stepNumber = parseInt(dropZone.dataset.step);
        
        // Enhanced debugging
        console.log(`Drop event: component=${component}, step=${stepNumber}`);
        console.log(`Component name: ${getComponentName(component)}`);
        
        dropZone.classList.remove('drag-over');
        
        // Create Arabic text element with proper styling
        const arabicText = getComponentName(component);
        dropZone.innerHTML = `<div class="text-gray-700 font-bold" dir="rtl">${arabicText}</div>`;
        
        gameCurrentAnswers[stepNumber - 1] = component;
        
        // Show success feedback
        showNotification(`تم إسقاط "${arabicText}" في الخطوة ${stepNumber}`, 'info');
        
        console.log(`Successfully dropped: ${component} -> ${arabicText} in step ${stepNumber}`);
        console.log('Current answers:', gameCurrentAnswers);
    } else {
        console.log('Error: Could not find valid drop zone');
    }
}

function getComponentName(component) {
    const names = {
        'camera-support': 'تركيب حاملات الكاميرات',
        'camera-install': 'تركيب الكاميرات',
        'cables': 'توصيل الكابلات',
        'nvr': 'تركيب NVR (مسجل الفيديو الشبكي)',
        'software': 'تثبيت وتشغيل البرامج',
        'testing': 'اختبار النظام والتأكد من التشغيل'
    };
    return names[component] || component;
}

function checkGameAnswer() {
    let correct = true;
    const dropZones = document.querySelectorAll('.drop-zone');
    
    dropZones.forEach((zone, index) => {
        const expectedComponent = gameCorrectOrder[index];
        const actualComponent = gameCurrentAnswers[index];
        
        if (actualComponent === expectedComponent) {
            zone.classList.add('correct');
        } else {
            correct = false;
            zone.classList.remove('correct');
        }
    });
    
    if (correct && gameCurrentAnswers.length === gameCorrectOrder.length) {
        showNotification('إجابة صحيحة! أحسنت!', 'success');
    } else {
        showNotification('الترتيب غير صحيح. حاول مرة أخرى.', 'error');
    }
}

function resetGame() {
    gameCurrentAnswers = [];
    const dropZones = document.querySelectorAll('.drop-zone');
    dropZones.forEach((zone, index) => {
        zone.classList.remove('correct', 'drag-over');
        zone.innerHTML = `<div class="text-gray-500">الخطوة ${index + 1}</div>`;
    });
}

document.addEventListener('DOMContentLoaded', () => {
    // Initialize simulation
    const startSimButton = document.getElementById('start-simulation');
    if (startSimButton) {
        resetSimulation();
    }
    
    // Initialize quiz only if quiz container exists
    const quizContainer = document.getElementById('quiz-container');
    if (quizContainer) {
        // Don't load quiz automatically, wait for user to click start
        console.log('Quiz container found');
    }
    
    // Add enter key support for chat
    const chatInput = document.getElementById('chat-input');
    if (chatInput) {
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendChatMessage();
            }
        });
    }
    
    // Debug: Check if video element exists
    const videoElement = document.getElementById('main-video');
    if (videoElement) {
        console.log('Video element found');
    } else {
        console.log('Video element not found');
    }
    
    // Initialize drag and drop functionality
    initializeDragAndDrop();
    
    // Reset game to show Arabic step names
    resetGame();
    
    // Debug: Check drag and drop elements
    const dragItems = document.querySelectorAll('.drag-item');
    const dropZones = document.querySelectorAll('.drop-zone');
    console.log(`Found ${dragItems.length} drag items and ${dropZones.length} drop zones`);
    console.log('Drag and drop initialized');
    
    // Test drag and drop functionality
    if (dragItems.length > 0 && dropZones.length > 0) {
        console.log('Drag and drop elements found and initialized successfully');
    } else {
        console.log('Warning: Drag and drop elements not found');
    }
});
