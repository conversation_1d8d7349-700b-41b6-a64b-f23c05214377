<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمن المعلومات - دورة الشبكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .module-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        }
        
        .interactive-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .interactive-card:hover {
            transform: translateY(-5px);
        }
        
        .quiz-option {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .quiz-option:hover {
            background-color: #e0e7ff;
            transform: translateX(10px);
            border-color: #3b82f6;
        }
        
        .quiz-option.correct {
            background-color: #dcfce7;
            border-color: #22c55e;
        }
        
        .quiz-option.incorrect {
            background-color: #fee2e2;
            border-color: #ef4444;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-red-50 to-pink-100 text-gray-800">

    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-reverse space-x-2">
                    <i class="fas fa-shield-alt text-red-600 text-2xl"></i>
                    <span class="font-bold text-xl text-gray-800">أمن المعلومات</span>
                </div>
                <div class="flex space-x-reverse space-x-4">
                    <a href="/" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-home ml-1"></i>
                        الرئيسية
                    </a>
                    <button onclick="completeModule()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-check ml-1"></i>
                        إكمال الوحدة
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header class="bg-gradient-to-r from-red-600 via-pink-600 to-purple-600 text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <div class="mb-8">
                <i class="fas fa-shield-alt text-8xl"></i>
            </div>
            <h1 class="text-5xl font-bold mb-4">أمن المعلومات</h1>
            <p class="text-xl">حماية البيانات والأنظمة من التهديدات الرقمية</p>
        </div>
    </header>

    <!-- Learning Objectives -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold mb-6 text-center">
                    <i class="fas fa-bullseye text-purple-600 ml-2"></i>
                    أهداف التعلم
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>فهم مبادئ الأمان الثلاثة (CIA)</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>التعرف على التهديدات الشائعة</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>تطبيق استراتيجيات الحماية</p>
                    </div>
                    <div class="learning-objective-item">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-2"></i>
                        <p>إنشاء كلمات مرور قوية</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CIA Triad Interactive -->
    <section class="py-12 px-4 bg-gray-50">
        <div class="container mx-auto max-w-6xl">
            <h2 class="text-4xl font-bold text-center mb-12">مثلث الأمان (CIA)</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                
                <!-- Confidentiality -->
                <div class="interactive-card bg-white p-8 rounded-2xl shadow-lg text-center transform hover:scale-105 transition-all duration-300">
                    <div>
                        <i class="fas fa-lock text-blue-600 text-6xl mb-6"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">السرية</h3>
                    <p class="text-gray-600 mb-6">ضمان عدم وصول الأشخاص غير المصرح لهم للمعلومات</p>
                    <div class="example-box bg-blue-50 p-4 rounded-lg">
                        <p class="text-sm font-semibold text-blue-800">مثال:</p>
                        <p class="text-sm text-blue-700">تشفير الرسائل الشخصية</p>
                    </div>
                </div>

                <!-- Integrity -->
                <div class="interactive-card bg-white p-8 rounded-2xl shadow-lg text-center transform hover:scale-105 transition-all duration-300">
                    <div>
                        <i class="fas fa-check-circle text-green-600 text-6xl mb-6"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">التكامل</h3>
                    <p class="text-gray-600 mb-6">ضمان عدم تغيير البيانات بشكل غير مصرح به</p>
                    <div class="example-box bg-green-50 p-4 rounded-lg">
                        <p class="text-sm font-semibold text-green-800">مثال:</p>
                        <p class="text-sm text-green-700">التأكد من صحة الملفات المحملة</p>
                    </div>
                </div>

                <!-- Availability -->
                <div class="interactive-card bg-white p-8 rounded-2xl shadow-lg text-center transform hover:scale-105 transition-all duration-300">
                    <div>
                        <i class="fas fa-clock text-purple-600 text-6xl mb-6"></i>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">الإتاحة</h3>
                    <p class="text-gray-600 mb-6">ضمان توفر المعلومات عند الحاجة إليها</p>
                    <div class="example-box bg-purple-50 p-4 rounded-lg">
                        <p class="text-sm font-semibold text-purple-800">مثال:</p>
                        <p class="text-sm text-purple-700">عمل النسخ الاحتياطية</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Password Strength Checker -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-key text-blue-600 ml-2"></i>
                    تحدي كلمة المرور القوية
                </h2>
                <div class="mb-6">
                    <label for="password-input" class="block text-lg font-semibold mb-2">أدخل كلمة مرور:</label>
                    <input type="password" id="password-input" class="w-full p-4 border-2 border-gray-300 rounded-lg text-lg focus:border-blue-500 focus:outline-none" placeholder="اكتب كلمة مرور قوية...">
                </div>
                
                <!-- Strength Meter -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-semibold">قوة كلمة المرور:</span>
                        <span id="strength-text" class="text-sm font-bold text-gray-500">ضعيف</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div id="strength-bar" class="h-3 rounded-full transition-all duration-500 bg-red-500" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Requirements Checklist -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                    <div class="requirement-item flex items-center p-3 bg-gray-50 rounded-lg" id="length-req">
                        <i class="fas fa-times text-red-500 ml-2"></i>
                        <span>12 حرف على الأقل</span>
                    </div>
                    <div class="requirement-item flex items-center p-3 bg-gray-50 rounded-lg" id="uppercase-req">
                        <i class="fas fa-times text-red-500 ml-2"></i>
                        <span>أحرف كبيرة</span>
                    </div>
                    <div class="requirement-item flex items-center p-3 bg-gray-50 rounded-lg" id="lowercase-req">
                        <i class="fas fa-times text-red-500 ml-2"></i>
                        <span>أحرف صغيرة</span>
                    </div>
                    <div class="requirement-item flex items-center p-3 bg-gray-50 rounded-lg" id="numbers-req">
                        <i class="fas fa-times text-red-500 ml-2"></i>
                        <span>أرقام</span>
                    </div>
                    <div class="requirement-item flex items-center p-3 bg-gray-50 rounded-lg" id="symbols-req">
                        <i class="fas fa-times text-red-500 ml-2"></i>
                        <span>رموز خاصة</span>
                    </div>
                    <div class="requirement-item flex items-center p-3 bg-gray-50 rounded-lg" id="common-req">
                        <i class="fas fa-times text-red-500 ml-2"></i>
                        <span>غير شائعة</span>
                    </div>
                </div>

                <!-- Generate Strong Password -->
                <div class="text-center">
                    <button onclick="generateStrongPassword()" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-magic ml-2"></i>
                        توليد كلمة مرور قوية
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Threats Quiz -->
    <section class="py-12 px-4 bg-gray-50">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold text-center mb-8">
                    <i class="fas fa-question-circle text-red-600 ml-2"></i>
                    اختبر معرفتك بالتهديدات الأمنية
                </h2>

                <!-- Quiz Question 1 -->
                <div class="quiz-question mb-8">
                    <h3 class="text-xl font-semibold mb-4">1. ما هو التصيد الاحتيالي (Phishing)؟</h3>
                    <div class="space-y-3">
                        <div class="quiz-option p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 transition-colors" onclick="selectAnswer(this, false)">
                            نوع من الفيروسات التي تدمر الملفات
                        </div>
                        <div class="quiz-option p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 transition-colors" onclick="selectAnswer(this, true)">
                            محاولة خداع المستخدمين للحصول على معلوماتهم الشخصية
                        </div>
                        <div class="quiz-option p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 transition-colors" onclick="selectAnswer(this, false)">
                            برنامج لحماية الحاسوب من الفيروسات
                        </div>
                        <div class="quiz-option p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 transition-colors" onclick="selectAnswer(this, false)">
                            طريقة لتسريع الإنترنت
                        </div>
                    </div>
                </div>

                <!-- Quiz Question 2 -->
                <div class="quiz-question mb-8">
                    <h3 class="text-xl font-semibold mb-4">2. أي من الآتي يعتبر أقوى كلمة مرور؟</h3>
                    <div class="space-y-3">
                        <div class="quiz-option p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 transition-colors" onclick="selectAnswer(this, false)">
                            <code class="font-mono">password123</code>
                        </div>
                        <div class="quiz-option p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 transition-colors" onclick="selectAnswer(this, false)">
                            <code class="font-mono">Ahmed2024</code>
                        </div>
                        <div class="quiz-option p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 transition-colors" onclick="selectAnswer(this, true)">
                            <code class="font-mono">M7md@2024$aL</code>
                        </div>
                        <div class="quiz-option p-4 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-500 transition-colors" onclick="selectAnswer(this, false)">
                            <code class="font-mono">qwerty12345</code>
                        </div>
                    </div>
                </div>

                <!-- Quiz Results -->
                <div id="quiz-results" class="text-center hidden">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                        <i class="fas fa-trophy text-yellow-500 text-4xl mb-4"></i>
                        <h3 class="text-2xl font-bold text-green-800 mb-2">أحسنت!</h3>
                        <p class="text-green-700">لقد أجبت على <span id="score">0</span> من 2 إجابات صحيحة</p>
                        <button onclick="restartQuiz()" class="mt-4 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                            إعادة المحاولة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tips Section -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-6xl">
            <h2 class="text-4xl font-bold text-center mb-12">نصائح أمنية مهمة</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="tip-card bg-gradient-to-br from-blue-500 to-purple-600 text-white p-6 rounded-2xl shadow-lg">
                    <i class="fas fa-shield-alt text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold mb-3">استخدم المصادقة الثنائية</h3>
                    <p class="text-blue-100">أضف طبقة حماية إضافية لحساباتك المهمة</p>
                </div>
                <div class="tip-card bg-gradient-to-br from-green-500 to-teal-600 text-white p-6 rounded-2xl shadow-lg">
                    <i class="fas fa-sync-alt text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold mb-3">حدث برامجك باستمرار</h3>
                    <p class="text-green-100">التحديثات تحتوي على إصلاحات أمنية مهمة</p>
                </div>
                <div class="tip-card bg-gradient-to-br from-red-500 to-pink-600 text-white p-6 rounded-2xl shadow-lg">
                    <i class="fas fa-wifi text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold mb-3">تجنب الشبكات العامة</h3>
                    <p class="text-red-100">لا تصل للحسابات المهمة عبر WiFi عام</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2025 دورة الشبكات وأمن المعلومات</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/assets/js/main.js"></script>
    <script src="/assets/js/security.js"></script>
    <script>
        function completeModule() {
            let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
            progress.security = true;
            localStorage.setItem('courseProgress', JSON.stringify(progress));
            
            alert('تم إكمال وحدة أمن المعلومات بنجاح! 🎉');
            
            // Redirect back to main page
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        }

        // Password strength checker
        document.getElementById('password-input').addEventListener('input', function() {
            const password = this.value;
            checkPasswordStrength(password);
        });

        function checkPasswordStrength(password) {
            let score = 0;
            const requirements = {
                'length-req': password.length >= 12,
                'uppercase-req': /[A-Z]/.test(password),
                'lowercase-req': /[a-z]/.test(password),
                'numbers-req': /\d/.test(password),
                'symbols-req': /[!@#$%^&*(),.?":{}|<>]/.test(password),
                'common-req': !isCommonPassword(password)
            };

            Object.keys(requirements).forEach(reqId => {
                const element = document.getElementById(reqId);
                const icon = element.querySelector('i');
                
                if (requirements[reqId]) {
                    score++;
                    icon.className = 'fas fa-check text-green-500 ml-2';
                    element.classList.add('bg-green-50');
                    element.classList.remove('bg-gray-50');
                } else {
                    icon.className = 'fas fa-times text-red-500 ml-2';
                    element.classList.add('bg-gray-50');
                    element.classList.remove('bg-green-50');
                }
            });

            updateStrengthMeter(score);
        }

        function updateStrengthMeter(score) {
            const percentage = (score / 6) * 100;
            const strengthBar = document.getElementById('strength-bar');
            const strengthText = document.getElementById('strength-text');

            strengthBar.style.width = percentage + '%';

            if (percentage < 33) {
                strengthBar.className = 'h-3 rounded-full transition-all duration-500 bg-red-500';
                strengthText.textContent = 'ضعيف';
                strengthText.className = 'text-sm font-bold text-red-500';
            } else if (percentage < 66) {
                strengthBar.className = 'h-3 rounded-full transition-all duration-500 bg-yellow-500';
                strengthText.textContent = 'متوسط';
                strengthText.className = 'text-sm font-bold text-yellow-500';
            } else {
                strengthBar.className = 'h-3 rounded-full transition-all duration-500 bg-green-500';
                strengthText.textContent = 'قوي';
                strengthText.className = 'text-sm font-bold text-green-500';
            }
        }

        function isCommonPassword(password) {
            const commonPasswords = ['password', '123456', 'password123', 'admin', 'qwerty', 'letmein'];
            return commonPasswords.some(common => password.toLowerCase().includes(common));
        }

        function generateStrongPassword() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
            let password = '';
            
            // Ensure at least one of each required type
            password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
            password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
            password += '0123456789'[Math.floor(Math.random() * 10)];
            password += '!@#$%^&*()'[Math.floor(Math.random() * 10)];
            
            // Fill the rest randomly
            for (let i = 4; i < 16; i++) {
                password += chars[Math.floor(Math.random() * chars.length)];
            }
            
            // Shuffle the password
            password = password.split('').sort(() => Math.random() - 0.5).join('');
            
            document.getElementById('password-input').value = password;
            checkPasswordStrength(password);
        }

        // Quiz functionality
        let quizScore = 0;
        let questionsAnswered = 0;

        function selectAnswer(element, isCorrect) {
            // Disable all options in this question
            const questionDiv = element.closest('.quiz-question');
            const options = questionDiv.querySelectorAll('.quiz-option');
            
            options.forEach(option => {
                option.style.pointerEvents = 'none';
            });

            if (isCorrect) {
                element.classList.add('correct');
                quizScore++;
            } else {
                element.classList.add('incorrect');
                // Show correct answer
                options.forEach(option => {
                    if (option.getAttribute('onclick').includes('true')) {
                        option.classList.add('correct');
                    }
                });
            }

            questionsAnswered++;
            
            if (questionsAnswered === 2) {
                setTimeout(() => {
                    showQuizResults();
                }, 1500);
            }
        }

        function showQuizResults() {
            document.getElementById('score').textContent = quizScore;
            document.getElementById('quiz-results').classList.remove('hidden');
        }

        function restartQuiz() {
            quizScore = 0;
            questionsAnswered = 0;
            
            document.querySelectorAll('.quiz-option').forEach(option => {
                option.classList.remove('correct', 'incorrect');
                option.style.pointerEvents = 'auto';
            });
            
            document.getElementById('quiz-results').classList.add('hidden');
        }
    </script>
</body>
</html>
