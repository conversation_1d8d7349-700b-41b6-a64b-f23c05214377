PROJECT SUMMARY: IT Collaborator 2025 - Networking and Information Security Training Course

OVERVIEW:
A comprehensive web-based training platform in networking and information security. The application is designed for team collaboration with network access, enabling teams to learn together. The platform offers a standalone experience, fully operational without internet connection after initial setup.

KEY FEATURES:
- Dual mode: Web application for teams + Desktop app for individuals
- Team collaboration: Multiple users can access simultaneously
- Network access: Share with team members via IP address
- Modular learning system covering various topics in networking and security
- Interactive quizzes and progress tracking
- Cross-platform support with builds for Windows, Linux, and macOS
- Certificate generation upon course completion
- Standalone desktop application (no internet required after installation)
- Progress tracking with local storage
- Arabic support with RTL layout
- Responsive design (desktop, tablet, mobile)
- Interactive simulators and tools

TECH STACK:
- Flask for web application backend
- Electron for the desktop application
- Node.js for backend services
- Tailwind CSS and Font Awesome for UI design
- Various HTML modules for content delivery
- Arabic language support (RTL layout)
- Python 3.7+ for web server

PROJECT STRUCTURE:
Main Files:
- main.js - Sets up and configures the Electron app
- preload.js - Preload scripts to handle integrations
- package.json - Project metadata and scripts
- README.md - Project documentation

Directories:
- src/ - Contains HTML, CSS, JS for course modules
- assets/ - Includes custom styles and images
- pages/ - Individual HTML files for each module
- dist/ - Built executables (generated after build)
- node_modules/ - Dependencies

COURSE MODULES:
1. مقدمة تحفيزية (Motivational Introduction) - Why these skills matter
2. أساسيات الشبكات (Network Fundamentals) - Basic networking concepts
3. أمن المعلومات (Information Security) - Data and system protection
4. أنظمة الإنذار (Alarm Systems) - Early threat detection
5. الحماية من الحرائق (Fire Protection) - Fire protection systems
6. المراقبة بالفيديو (Video Surveillance) - Surveillance and protection systems
7. أنظمة الحماية من الحرائق (Fire Protection Systems) - Components and types of alarm systems

SETUP AND RUN:
Prerequisites:
- Python 3.7+ (for web app)
- Node.js (version 16 or higher) (for desktop app)
- npm (comes with Node.js)

Quick Start Options:
1. Using Batch File (Windows): Double-click install-and-run.bat
2. Using PowerShell (Windows): Run setup-for-team.ps1
3. Using Python:
   - Run: python app.py
4. Manual Commands:
   - Install node dependencies: npm install
   - Start desktop application: npm start
   - Build Windows executable: npm run build-win
   - Build Linux executable: npm run build-linux
   - Build macOS executable: npm run build-mac

TEAM ACCESS
- Local: http://127.0.0.1:5000
- Network: http://[YOUR-IP]:5000

BUILD OUTPUT:
Resulting executables are in the dist/ folder:
- Windows: IT Collaborator 2025 Setup.exe (installer) and portable exe
- Linux: IT Collaborator 2025.AppImage
- macOS: IT Collaborator 2025.dmg

AUTHOR:
Kamel Balla - IT Collaborator 2025
© 2025 أكاديمية الشبكات وأمن المعلومات Kamel Balla. جميع الحقوق محفوظة.

LICENSE:
MIT License

NOTES:
- The project is an educational platform (both web and desktop)
- Supports Arabic language with RTL layout
- Progress tracking excludes the introduction module (6 core modules counted)
- All course content is embedded for offline use
- Web application enables team learning and collaboration
- CSS styling fixed for network access (all pages now display correctly)
- Includes interactive elements: password checker, network simulator, alarm system, OSI model visualizer, IP calculator
