/* Import Cairo font */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* Global Styles */
* {
    font-family: 'Cairo', sans-serif;
    scroll-behavior: smooth;
}

/* Custom Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -15px, 0);
    }
    70% {
        transform: translate3d(0, -7px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes glow {
    0% { 
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% { 
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
    }
    100% { 
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 1s ease-out;
}

.animate-fade-in-delay {
    animation: fadeIn 1s ease-out 0.3s both;
}

.animate-fade-in-delay-2 {
    animation: fadeIn 1s ease-out 0.6s both;
}

.animate-slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

.animate-bounce-in {
    animation: bounce 1s ease-out;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Custom Gradient Backgrounds */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-security {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.gradient-network {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.gradient-alarm {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

/* Module Cards */
.module-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.module-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* Interactive Elements */
.interactive-card {
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.interactive-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.interactive-card:hover::before {
    left: 100%;
}

.interactive-card:hover {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    transform: translateY(-5px);
}

/* Quiz Options */
.quiz-option {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
    position: relative;
}

.quiz-option:hover {
    background-color: #e0e7ff;
    transform: translateX(10px);
    border-color: #3b82f6;
}

.quiz-option.correct {
    background-color: #dcfce7;
    border-color: #22c55e;
    animation: bounce 0.6s ease-out;
}

.quiz-option.incorrect {
    background-color: #fee2e2;
    border-color: #ef4444;
    animation: shake 0.5s ease-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Progress Bar */
.progress-bar {
    background: linear-gradient(90deg, #4f46e5 0%, #06b6d4 100%);
    height: 8px;
    border-radius: 4px;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Navigation */
.nav-link {
    position: relative;
    font-weight: 500;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Floating Particles */
.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: float-particle 6s infinite linear;
}

.particle:nth-child(1) {
    left: 10%;
    animation-delay: 0s;
    animation-duration: 8s;
}

.particle:nth-child(2) {
    left: 20%;
    animation-delay: 1s;
    animation-duration: 10s;
}

.particle:nth-child(3) {
    left: 30%;
    animation-delay: 2s;
    animation-duration: 7s;
}

.particle:nth-child(4) {
    left: 40%;
    animation-delay: 3s;
    animation-duration: 9s;
}

.particle:nth-child(5) {
    left: 50%;
    animation-delay: 4s;
    animation-duration: 6s;
}

@keyframes float-particle {
    0% {
        transform: translateY(100vh) translateX(0px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-10vh) translateX(100px);
        opacity: 0;
    }
}

/* Loading Animation */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px 12px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 14px;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
    .module-card {
        margin-bottom: 1rem;
    }
    
    .floating-particles {
        display: none;
    }
    
    .nav-link::after {
        display: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .module-card {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        color: #f9fafb;
    }
}

/* Print Styles */
@media print {
    .floating-particles,
    .nav-link::after,
    .interactive-card::before {
        display: none !important;
    }
    
    .module-card {
        box-shadow: none !important;
        border: 1px solid #e5e7eb !important;
    }
    
    .gradient-bg {
        background: #667eea !important;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles */
button:focus,
.quiz-option:focus {
    outline: 3px solid #3b82f6;
    outline-offset: 2px;
}

/* Logo Styles */
.logo-image {
    display: inline-block;
    background-color: #ffffff;
    padding: 6px;
    border-radius: 8px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    max-width: 200px;
    min-width: 100px;
    height: 80px !important;
    width: auto !important;
}

.logo-image:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Main Header Logo */
.main-header-logo {
    display: block;
    max-width: 400px;
    width: 100%;
    height: auto;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    transition: all 0.6s ease;
    border: 3px solid rgba(255, 255, 255, 0.3);
}

.main-header-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.6);
}

@media (max-width: 768px) {
    .main-header-logo {
        max-width: 280px;
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .main-header-logo {
        max-width: 220px;
        padding: 12px;
    }
}

/* Certificate Animation */
.certificate-glow {
    animation: certificate-pulse 2s ease-in-out infinite;
}

@keyframes certificate-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.4);
    }
    70% {
        box-shadow: 0 0 0 20px rgba(251, 191, 36, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(251, 191, 36, 0);
    }
}

/* Success Animation */
.success-checkmark {
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
    animation: draw-checkmark 1s ease-out forwards;
}

@keyframes draw-checkmark {
    to {
        stroke-dashoffset: 0;
    }
}
