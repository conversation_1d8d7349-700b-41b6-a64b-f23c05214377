<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مقدمة الدورة - دورة الشبكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .module-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        }
        
        .float-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-blue-100 text-gray-800">

    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center space-x-reverse space-x-2">
                    <i class="fas fa-book-reader text-blue-600 text-2xl"></i>
                    <span class="font-bold text-xl text-gray-800">مقدمة الدورة</span>
                </div>
                <div class="flex space-x-reverse space-x-4">
                    <a href="/" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-home ml-1"></i>
                        الرئيسية
                    </a>
                    <button onclick="completeModule()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-check ml-1"></i>
                        إكمال الوحدة
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <header class="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <div class="float-animation inline-block mb-8 bg-white bg-opacity-20 p-6 rounded-full">
                <i class="fas fa-graduation-cap text-6xl mb-4"></i>
            </div>
            <h1 class="text-5xl md:text-7xl font-bold mb-6">
                دورة تدريبية متكاملة
            </h1>
            <h2 class="text-3xl md:text-4xl font-semibold mb-8 text-blue-100">
                الشبكات وأمن المعلومات للمبتدئين
            </h2>
            <p class="text-xl md:text-2xl mb-12 opacity-90 max-w-4xl mx-auto leading-relaxed">
                رحلة شاملة نحو إتقان أساسيات الشبكات، أمن المعلومات، وأنظمة الحماية الحديثة
            </p>
            <div class="flex justify-center space-x-4 space-x-reverse">
                <button class="bg-white text-blue-600 px-8 py-3 rounded-full font-bold hover:bg-blue-50 transform hover:scale-105 transition-all duration-300 shadow-lg">
                    ابدأ التعلم
                </button>
            </div>
        </div>
    </header>

    <!-- Course Introduction Section -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-3xl font-bold mb-6 text-center">
                    <i class="fas fa-bullseye text-blue-600 ml-2"></i>
                    لماذا هذه المهارات مهمة؟
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="objective-item">
                        <i class="fas fa-shield-alt text-blue-500 text-xl mb-2"></i>
                        <p>فهم أهمية أمن المعلومات في العصر الرقمي</p>
                    </div>
                    <div class="objective-item">
                        <i class="fas fa-lock text-blue-500 text-xl mb-2"></i>
                        <p>حماية البيانات والشبكات من التهديدات</p>
                    </div>
                    <div class="objective-item">
                        <i class="fas fa-briefcase text-blue-500 text-xl mb-2"></i>
                        <p>استكشاف الفرص الوظيفية في المجال</p>
                    </div>
                    <div class="objective-item">
                        <i class="fas fa-laptop-code text-blue-500 text-xl mb-2"></i>
                        <p>التطبيق العملي للمفاهيم الأمنية</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Learning Objectives -->
    <section class="py-12 px-4 bg-gray-50">
        <div class="container mx-auto max-w-6xl">
            <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl p-8 mb-8">
                <div class="flex items-center mb-6">
                    <i class="fas fa-bullseye text-blue-600 text-2xl ml-4"></i>
                    <h3 class="text-3xl font-bold text-gray-800">أهداف التعلم</h3>
                </div>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
                        <div class="flex items-start">
                            <i class="fas fa-shield-alt text-blue-500 text-xl ml-4 mt-1"></i>
                            <p class="text-lg">فهم أهمية أمن المعلومات في العصر الرقمي وتأثيره على المؤسسات والأفراد</p>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
                        <div class="flex items-start">
                            <i class="fas fa-lock text-blue-500 text-xl ml-4 mt-1"></i>
                            <p class="text-lg">إدراك الحاجة لحماية البيانات والشبكات في ظل التحديات الأمنية المتزايدة</p>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
                        <div class="flex items-start">
                            <i class="fas fa-briefcase text-blue-500 text-xl ml-4 mt-1"></i>
                            <p class="text-lg">استكشاف الفرص الوظيفية المتنوعة والواعدة في مجال أمن المعلومات</p>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
                        <div class="flex items-start">
                            <i class="fas fa-laptop-code text-blue-500 text-xl ml-4 mt-1"></i>
                            <p class="text-lg">تعزيز الدافعية للتعلم والتطبيق العملي في مجال أمن المعلومات</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Course Features -->
    <section class="py-12 px-4">
        <div class="container mx-auto max-w-6xl">
            <h2 class="text-4xl font-bold text-center mb-12">مميزات الدورة</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="feature-card bg-white p-6 rounded-2xl shadow-lg text-center">
                    <div class="mb-4">
                        <i class="fas fa-clock text-4xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">40 ساعة تدريبية</h3>
                    <p class="text-gray-600">محتوى تعليمي مكثف ومتكامل</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-2xl shadow-lg text-center">
                    <div class="mb-4">
                        <i class="fas fa-tasks text-4xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">تطبيقات عملية</h3>
                    <p class="text-gray-600">مشاريع وتدريبات واقعية</p>
                </div>
                <div class="feature-card bg-white p-6 rounded-2xl shadow-lg text-center">
                    <div class="mb-4">
                        <i class="fas fa-certificate text-4xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">شهادة معتمدة</h3>
                    <p class="text-gray-600">عند إتمام الدورة بنجاح</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2025 دورة الشبكات وأمن المعلومات</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/assets/js/main.js"></script>
    <script src="/assets/js/introduction.js"></script>
    <script>
        function completeModule() {
            let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
            progress.introduction = true;
            localStorage.setItem('courseProgress', JSON.stringify(progress));
            
            alert('تم إكمال وحدة المقدمة بنجاح! 🎉');
            
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
        }
    </script>
</body>
</html>
