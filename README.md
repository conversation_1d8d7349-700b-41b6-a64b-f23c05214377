# IT Collaborator 2025 - Training Platform

A comprehensive web-based training course for Networking and Information Security, designed for both individual learning and team collaboration.

## 🚀 Quick Start

### Prerequisites
- Python 3.7+ installed

### Team Setup (Recommended)
1. **Run the team setup script:**
   ```powershell
   powershell -ExecutionPolicy Bypass -File "setup-for-team.ps1"
   ```

2. **Or start manually:**
   ```bash
   # Install Python dependencies
   pip install -r requirements.txt
   
   # Start the web application
   python app.py
   ```

3. **Access URLs:**
   - **Local access:** `http://127.0.0.1:5000`
   - **Network access:** `http://[YOUR-IP]:5000` (share with team)

### Quick Team Start
```bash
# Option 1: Use the web app batch file
start-web-app.bat

# Option 2: PowerShell setup
.\setup-for-team.ps1

# Option 3: Direct Python command
python app.py
```

## 🌟 Features

### Core Features
- ✅ **Web-Based**: Pure web application for maximum compatibility
- ✅ **Team Collaboration**: Multiple users can access simultaneously
- ✅ **Network Access**: Share with team members via IP address
- ✅ **Offline Capable**: No internet required after setup
- ✅ **Progress Tracking**: Individual progress saved locally
- ✅ **Interactive Learning**: Quizzes, simulators, and hands-on exercises
- ✅ **Certificate Generation**: Completion certificates for learners
- ✅ **Cross-Platform**: Works on any device with a web browser
- ✅ **Arabic Support**: RTL layout and Arabic content
- ✅ **Responsive Design**: Works on desktop, tablet, and mobile

### Interactive Elements
- 🔐 **Password Strength Checker**: Real-time password validation
- 🌐 **Network Simulator**: Interactive network topology builder
- 🔥 **Alarm System Simulator**: Emergency response training
- 📊 **OSI Model Visualizer**: Layer-by-layer network understanding
- 🧮 **IP Address Calculator**: Subnet and network calculations
- 🛠️ **Troubleshooting Tools**: Command-line simulation (ping, ipconfig, tracert)
- 📋 **Knowledge Quizzes**: Multiple-choice assessments
- 🎯 **Progress Tracking**: Module completion and overall progress
- 🎮 **Video Surveillance Drag & Drop**: Interactive component placement game with Arabic translations

## Application Structure

```
IT Collaborator 2025/
├── src/                    # Web application files
│   ├── index.html         # Main page
│   ├── pages/             # Course modules
│   ├── assets/            # CSS, JS, images
├── main.js                # Electron main process
├── preload.js             # Electron preload script
├── package.json           # Project configuration
└── dist/                  # Built executables (after build)
```

## 📚 Course Modules

1. **مقدمة تحفيزية** - Motivational Introduction (not counted in progress)
2. **أساسيات الشبكات** - Network Fundamentals
3. **أمن المعلومات** - Information Security
4. **أنظمة الإنذار** - Alarm Systems
5. **الحماية من الحرائق** - Fire Protection
6. **المراقبة بالفيديو** - Video Surveillance
   - 🎮 Interactive drag & drop component placement game
   - 📝 Arabic component naming with RTL support
   - 🎯 6-step installation sequence simulation
   - 🔧 Real-time feedback and progress tracking
7. **أنظمة الحماية من الحرائق** - Fire Protection Systems
8. **جدران حماية Sophos** - Sophos Firewalls

## 👥 Team Management

### For Team Leaders
- **Central Server**: Run the Flask app on one machine for team access
- **Individual Access**: Each team member can also run locally if needed
- **Progress Tracking**: Each user's progress is saved in their browser
- **Certificates**: Generated individually upon course completion
- **No User Management**: Simple access via shared URL

### Network Setup
1. **Find your IP address**: The setup script will show your network IP
2. **Share the URL**: Give team members `http://[YOUR-IP]:5000`
3. **Firewall**: May need to allow Python/Flask on port 5000
4. **WiFi**: Ensure all team members are on the same network

## 🛠️ Troubleshooting

### Common Issues

#### "Pages look basic/unstyled over network"
- **Fixed**: All CSS links have been updated to use local files
- **Solution**: Restart the application after any updates

#### "Team members can't access the application"
- **Check IP**: Verify the correct IP address is being used
- **Firewall**: Run as administrator and add firewall rule:
  ```cmd
  netsh advfirewall firewall add rule name="IT Collaborator 2025" dir=in action=allow protocol=TCP localport=5000
  ```
- **Network**: Ensure all devices are on the same network

#### "Application won't start"
- **Python**: Make sure Python 3.7+ is installed
- **Dependencies**: Run `pip install -r requirements.txt`
- **Port**: Check if port 5000 is already in use

### Manual Firewall Setup
```cmd
# Windows Command Prompt (as Administrator)
netsh advfirewall firewall add rule name="IT Collaborator 2025" dir=in action=allow protocol=TCP localport=5000
```

### Getting Your IP Address
```cmd
# Windows
ipconfig | findstr "IPv4"

# Linux/macOS
ifconfig | grep "inet "
```

## Author

**Kamel Balla** - IT Collaborator 2025

© 2025 أكاديمية الشبكات وأمن المعلومات Kamel Balla. جميع الحقوق محفوظة.

## License

MIT License - See LICENSE file for details.

## python -m http.server 8080
## python app.py