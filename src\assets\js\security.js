// Security Module Interactive Functions

let quizScore = 0;
let answeredQuestions = 0;
const totalQuestions = 2;

// Password Strength Checker
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password-input');
    const strengthBar = document.getElementById('strength-bar');
    const strengthText = document.getElementById('strength-text');

    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });
    }
});

function checkPasswordStrength(password) {
    const strengthBar = document.getElementById('strength-bar');
    const strengthText = document.getElementById('strength-text');
    
    let score = 0;
    let strength = '';
    let color = '';

    // Check requirements
    const requirements = {
        length: password.length >= 12,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        numbers: /\d/.test(password),
        symbols: /[!@#$%^&*(),.?":{}|<>]/.test(password),
        common: !isCommonPassword(password)
    };

    // Update requirement indicators
    updateRequirement('length-req', requirements.length);
    updateRequirement('uppercase-req', requirements.uppercase);
    updateRequirement('lowercase-req', requirements.lowercase);
    updateRequirement('numbers-req', requirements.numbers);
    updateRequirement('symbols-req', requirements.symbols);
    updateRequirement('common-req', requirements.common);

    // Calculate score
    score = Object.values(requirements).filter(Boolean).length;

    // Update strength bar and text
    if (score === 0) {
        strength = 'ضعيف جداً';
        color = '#ef4444';
        strengthBar.style.width = '0%';
    } else if (score <= 2) {
        strength = 'ضعيف';
        color = '#ef4444';
        strengthBar.style.width = '20%';
    } else if (score <= 4) {
        strength = 'متوسط';
        color = '#f59e0b';
        strengthBar.style.width = '50%';
    } else if (score <= 5) {
        strength = 'قوي';
        color = '#10b981';
        strengthBar.style.width = '80%';
    } else {
        strength = 'قوي جداً';
        color = '#059669';
        strengthBar.style.width = '100%';
    }

    strengthText.textContent = strength;
    strengthBar.style.backgroundColor = color;
}

function updateRequirement(elementId, met) {
    const element = document.getElementById(elementId);
    const icon = element.querySelector('i');
    
    if (met) {
        icon.className = 'fas fa-check text-green-500 ml-2';
        element.classList.add('bg-green-50');
        element.classList.remove('bg-gray-50');
    } else {
        icon.className = 'fas fa-times text-red-500 ml-2';
        element.classList.add('bg-gray-50');
        element.classList.remove('bg-green-50');
    }
}

function isCommonPassword(password) {
    const commonPasswords = [
        'password', '123456', 'password123', 'admin', 'qwerty',
        'letmein', 'welcome', 'monkey', '1234567890', 'abc123',
        'احمد123', 'محمد123', 'admin123', 'user123'
    ];
    return commonPasswords.includes(password.toLowerCase());
}

function generateStrongPassword() {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    let password = '';
    
    // Ensure at least one character from each category
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];
    
    // Fill the rest randomly
    const allChars = uppercase + lowercase + numbers + symbols;
    for (let i = 4; i < 16; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
    }
    
    // Shuffle the password
    password = password.split('').sort(() => Math.random() - 0.5).join('');
    
    document.getElementById('password-input').value = password;
    checkPasswordStrength(password);
    
    // Show success animation
    showNotification('تم توليد كلمة مرور قوية!', 'success');
}

// Quiz Functions
function selectAnswer(element, isCorrect) {
    // Disable all options in this question
    const questionDiv = element.closest('.quiz-question');
    const allOptions = questionDiv.querySelectorAll('.quiz-option');
    
    allOptions.forEach(option => {
        option.style.pointerEvents = 'none';
        option.classList.add('opacity-60');
    });

    // Mark the selected answer
    if (isCorrect) {
        element.classList.add('correct', 'border-green-500', 'bg-green-100');
        element.innerHTML += ' <i class="fas fa-check text-green-600 float-left"></i>';
        quizScore++;
    } else {
        element.classList.add('incorrect', 'border-red-500', 'bg-red-100');
        element.innerHTML += ' <i class="fas fa-times text-red-600 float-left"></i>';
        
        // Show correct answer
        allOptions.forEach(option => {
            if (option.onclick && option.onclick.toString().includes('true')) {
                option.classList.add('border-green-500', 'bg-green-100');
                option.innerHTML += ' <i class="fas fa-check text-green-600 float-left"></i>';
            }
        });
    }

    answeredQuestions++;
    
    // Show results if all questions answered
    if (answeredQuestions >= totalQuestions) {
        setTimeout(() => {
            showQuizResults();
        }, 1000);
    }
}

function showQuizResults() {
    const resultsDiv = document.getElementById('quiz-results');
    const scoreSpan = document.getElementById('score');
    
    scoreSpan.textContent = quizScore;
    resultsDiv.classList.remove('hidden');
    resultsDiv.scrollIntoView({ behavior: 'smooth' });
    
    // Save progress
    markModuleComplete();
}

function restartQuiz() {
    quizScore = 0;
    answeredQuestions = 0;
    
    // Reset all quiz options
    document.querySelectorAll('.quiz-option').forEach(option => {
        option.style.pointerEvents = 'auto';
        option.classList.remove('opacity-60', 'correct', 'incorrect', 'border-green-500', 'bg-green-100', 'border-red-500', 'bg-red-100');
        option.classList.add('border-gray-200');
        
        // Remove result icons
        const icons = option.querySelectorAll('.fa-check, .fa-times');
        icons.forEach(icon => icon.remove());
    });
    
    // Hide results
    document.getElementById('quiz-results').classList.add('hidden');
}

function markModuleComplete() {
    let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
    progress.security = true;
    localStorage.setItem('courseProgress', JSON.stringify(progress));
    
    showNotification('تم إكمال وحدة أمن المعلومات بنجاح!', 'success');
}

function completeModule() {
    markModuleComplete();
    setTimeout(() => {
        window.location.href = '../index.html';
    }, 1500);
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
    
    if (type === 'success') {
        notification.classList.add('bg-green-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-check-circle ml-2"></i>${message}`;
    } else if (type === 'error') {
        notification.classList.add('bg-red-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-exclamation-circle ml-2"></i>${message}`;
    } else {
        notification.classList.add('bg-blue-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-info-circle ml-2"></i>${message}`;
    }
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Animate out
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add scroll animations
function addScrollAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
            }
        });
    }, { threshold: 0.1 });

    document.querySelectorAll('.interactive-card, .learning-objective-item').forEach(el => {
        observer.observe(el);
    });
}

// Initialize scroll animations when page loads
document.addEventListener('DOMContentLoaded', addScrollAnimations);
