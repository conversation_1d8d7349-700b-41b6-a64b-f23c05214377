import os
import sys
import webbrowser
import threading
import time
from flask import Flask, render_template, send_from_directory, redirect, url_for, Response

# Get the directory where the script is located
base_dir = os.path.dirname(os.path.abspath(__file__))

# Create Flask app with proper static and template folders
app = Flask(__name__, 
           template_folder=os.path.join(base_dir, 'src'),
           static_folder=os.path.join(base_dir, 'src'))

# Set UTF-8 encoding for all responses
@app.after_request
def after_request(response):
    if response.content_type.startswith('text/html'):
        response.headers['Content-Type'] = 'text/html; charset=utf-8'
    return response

@app.route('/')
def index():
    response = send_from_directory(app.static_folder, 'index.html')
    response.headers['Content-Type'] = 'text/html; charset=utf-8'
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

@app.route('/pages/<path:filename>')
def serve_pages(filename):
    response = send_from_directory(os.path.join(app.static_folder, 'pages'), filename)
    if filename.endswith('.html'):
        response.headers['Content-Type'] = 'text/html; charset=utf-8'
    return response

@app.route('/assets/<path:filename>')
def serve_assets(filename):
    return send_from_directory(os.path.join(app.static_folder, 'assets'), filename)

@app.route('/assets/css/<path:filename>')
def serve_css(filename):
    return send_from_directory(os.path.join(app.static_folder, 'assets', 'css'), filename)

@app.route('/assets/js/<path:filename>')
def serve_js(filename):
    return send_from_directory(os.path.join(app.static_folder, 'assets', 'js'), filename)

@app.route('/assets/images/<path:filename>')
def serve_images(filename):
    return send_from_directory(os.path.join(app.static_folder, 'assets', 'images'), filename)

# Serve local CSS files if they exist, otherwise redirect to CDN
@app.route('/tailwind.min.css')
def serve_tailwind():
    local_path = os.path.join(app.static_folder, 'tailwind.min.css')
    if os.path.exists(local_path):
        return send_from_directory(app.static_folder, 'tailwind.min.css')
    else:
        return redirect('https://cdn.tailwindcss.com')

@app.route('/all.min.css')
def serve_fontawesome():
    local_path = os.path.join(app.static_folder, 'all.min.css')
    if os.path.exists(local_path):
        return send_from_directory(app.static_folder, 'all.min.css')
    else:
        return redirect('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css')

# Catch-all route for other files
@app.route('/<path:filename>')
def serve_static(filename):
    # First try in the static folder
    static_path = os.path.join(app.static_folder, filename)
    if os.path.exists(static_path):
        return send_from_directory(app.static_folder, filename)
    
    # Then try in the root directory
    root_path = os.path.join(base_dir, filename)
    if os.path.exists(root_path):
        return send_from_directory(base_dir, filename)
    
    # If not found, return 404
    return "File not found", 404

def open_browser():
    """Open the browser after a short delay"""
    time.sleep(1.5)
    webbrowser.open('http://127.0.0.1:5000')

if __name__ == '__main__':
    # Start browser in a separate thread
    if not os.environ.get('WERKZEUG_RUN_MAIN'):
        threading.Thread(target=open_browser).start()
    
    print("Starting IT Collaborator 2025 Web Application...")
    print("Application will open in your browser...")
    print("Local access: http://127.0.0.1:5000")
    print("Network access: http://[YOUR-IP]:5000")
    print("Close this window to stop the application.")
    
    app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)
