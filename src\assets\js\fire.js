// Enhanced Fire Protection System Simulator
// Comprehensive training system with advanced features

let systemActive = false;
let quizScore = 0;
let answeredQuestions = 0;
const totalQuestions = 8; // Expanded quiz system
let currentModule = 'basic';
let trainingProgress = {
    basic: { completed: false, score: 0 },
    advanced: { completed: false, score: 0 },
    emergency: { completed: false, score: 0 }
};
let alertHistory = [];
let systemUptime = 0;
let uptimeInterval = null;
let sensorData = {
    smoke: { level: 0, threshold: 75, status: 'normal' },
    heat: { level: 20, threshold: 80, status: 'normal' },
    flame: { level: 0, threshold: 50, status: 'normal' },
    co: { level: 0, threshold: 35, status: 'normal' }
};
let simulationScenarios = [
    { name: 'مكتب صغير', risk: 'low', duration: 30 },
    { name: 'مستودع', risk: 'medium', duration: 45 },
    { name: 'مصنع كيميائي', risk: 'high', duration: 60 },
    { name: 'مركز بيانات', risk: 'critical', duration: 90 }
];

// Function to convert Arabic-Indic digits to Western digits
function convertToWesternDigits(str) {
    const arabicDigits = '٠١٢٣٤٥٦٧٨٩';
    const westernDigits = '0123456789';
    
    return str.replace(/[٠-٩]/g, function(match) {
        return westernDigits[arabicDigits.indexOf(match)];
    });
}

// System Control Functions
function toggleSystem() {
    const toggleBtn = document.getElementById('system-toggle');
    const testBtn = document.getElementById('test-btn');
    const statusDiv = document.getElementById('system-status');
    const sensorTriggers = document.querySelectorAll('.sensor-trigger');
    const statusIndicators = document.querySelectorAll('.status-indicator');

    if (!systemActive) {
        // Activate system
        systemActive = true;
        toggleBtn.innerHTML = '<i class="fas fa-power-off ml-2"></i> إيقاف النظام';
        toggleBtn.classList.remove('bg-red-500', 'hover:bg-red-600');
        toggleBtn.classList.add('bg-green-500', 'hover:bg-green-600');
        
        testBtn.disabled = false;
        testBtn.classList.remove('opacity-50');
        
        statusDiv.className = 'bg-green-100 border border-green-200 p-4 rounded-lg text-center';
        statusDiv.innerHTML = '<p class="text-green-700">النظام نشط - جميع أجهزة الاستشعار تعمل</p>';
        
        // Enable sensor triggers
        sensorTriggers.forEach(trigger => {
            trigger.disabled = false;
            trigger.classList.remove('opacity-50');
        });

        // Change sensor indicators to green (active)
        statusIndicators.forEach(indicator => {
            indicator.classList.remove('bg-red-500');
            indicator.classList.add('bg-green-500');
        });

        addAlert('تم تشغيل نظام الحماية من الحرائق بنجاح', 'success');
        
    } else {
        // Deactivate system
        systemActive = false;
        toggleBtn.innerHTML = '<i class="fas fa-power-off ml-2"></i> تشغيل النظام';
        toggleBtn.classList.remove('bg-green-500', 'hover:bg-green-600');
        toggleBtn.classList.add('bg-red-500', 'hover:bg-red-600');
        
        testBtn.disabled = true;
        testBtn.classList.add('opacity-50');
        
        statusDiv.className = 'bg-red-100 border border-red-200 p-4 rounded-lg text-center';
        statusDiv.innerHTML = '<p class="text-red-700">النظام متوقف - انقر على "تشغيل النظام" للبدء</p>';
        
        // Disable sensor triggers
        sensorTriggers.forEach(trigger => {
            trigger.disabled = true;
            trigger.classList.add('opacity-50');
        });

        // Change sensor indicators to red (inactive)
        statusIndicators.forEach(indicator => {
            indicator.classList.remove('bg-green-500');
            indicator.classList.add('bg-red-500');
        });

        addAlert('تم إيقاف نظام الحماية من الحرائق', 'info');
    }
}

function testSystem() {
    if (!systemActive) return;
    
    // Flash all indicators
    const statusIndicators = document.querySelectorAll('.status-indicator');
    statusIndicators.forEach(indicator => {
        indicator.classList.add('animate-pulse');
        indicator.classList.remove('bg-green-500');
        indicator.classList.add('bg-yellow-500');
    });

    // Play test simulation
    addAlert('🔔 اختبار النظام - جميع أجهزة كشف الحريق والإطفاء تعمل بشكل طبيعي', 'warning');
    
    // Reset indicators after 3 seconds
    setTimeout(() => {
        statusIndicators.forEach(indicator => {
            indicator.classList.remove('animate-pulse', 'bg-yellow-500');
            indicator.classList.add('bg-green-500');
        });
    }, 3000);
}

function triggerSensor(sensorType) {
    if (!systemActive) return;

    const sensorNames = {
        smoke: 'كاشف الدخان',
        heat: 'كاشف الحرارة',
        flame: 'كاشف اللهب',
        sprinkler: 'رشاش المياه'
    };

    const alertTypes = {
        smoke: '⚠️ تحذير: تم اكتشاف دخان كثيف!',
        heat: '🔥 خطر: ارتفاع شديد في درجة الحرارة!',
        flame: '🔥 تحذير: تم اكتشاف لهب مباشر!',
        sprinkler: '💧 تم تفعيل نظام الرش التلقائي'
    };

    const colors = {
        smoke: 'bg-gray-600',
        heat: 'bg-red-500',
        flame: 'bg-orange-500',
        sprinkler: 'bg-blue-500'
    };

    // Flash specific sensor
    const sensor = document.getElementById(`${sensorType}-sensor`);
    const indicator = sensor.querySelector('.status-indicator');
    
    indicator.classList.add('animate-pulse');
    indicator.classList.remove('bg-green-500');
    indicator.classList.add(colors[sensorType]);

    // Add alert
    addAlert(alertTypes[sensorType], sensorType === 'sprinkler' ? 'info' : 'danger');

    // Automatically trigger sprinkler system if fire detected
    if (sensorType === 'heat' || sensorType === 'flame') {
        setTimeout(() => {
            triggerSensor('sprinkler');
        }, 1000);
    }

    // Reset after 5 seconds
    setTimeout(() => {
        indicator.classList.remove('animate-pulse', colors[sensorType]);
        indicator.classList.add('bg-green-500');
    }, 5000);
}

function resetSystem() {
    // Clear alert log
    const alertLog = document.getElementById('alert-log');
    alertLog.innerHTML = '<div class="text-center text-gray-500 text-sm">لا توجد تنبيهات</div>';
    
    // Reset system state
    if (systemActive) {
        toggleSystem(); // Turn off system
    }
    
    addAlert('تم إعادة تعيين النظام', 'info');
}

function addAlert(message, type = 'info') {
    const alertLog = document.getElementById('alert-log');
    const timestamp = convertToWesternDigits(new Date().toLocaleTimeString('ar-SA'));
    
    // Remove placeholder if exists
    const placeholder = alertLog.querySelector('.text-gray-500');
    if (placeholder) {
        placeholder.remove();
    }

    const alertDiv = document.createElement('div');
    alertDiv.className = `p-3 rounded-lg text-sm animate-fade-in ${getAlertStyle(type)}`;
    alertDiv.innerHTML = `
        <div class="flex justify-between items-start">
            <span>${message}</span>
            <span class="text-xs opacity-75">${timestamp}</span>
        </div>
    `;

    // Add to top of log
    alertLog.insertBefore(alertDiv, alertLog.firstChild);

    // Keep only last 10 alerts
    while (alertLog.children.length > 10) {
        alertLog.removeChild(alertLog.lastChild);
    }

    // Scroll to top
    alertLog.scrollTop = 0;
}

function getAlertStyle(type) {
    const styles = {
        success: 'bg-green-100 border border-green-200 text-green-800',
        warning: 'bg-yellow-100 border border-yellow-200 text-yellow-800',
        danger: 'bg-red-100 border border-red-200 text-red-800',
        info: 'bg-blue-100 border border-blue-200 text-blue-800'
    };
    return styles[type] || styles.info;
}

// Quiz Functions
function selectAnswer(element, isCorrect) {
    // Disable all options in this question
    const questionDiv = element.closest('.quiz-question');
    const allOptions = questionDiv.querySelectorAll('.quiz-option');
    
    allOptions.forEach(option => {
        option.style.pointerEvents = 'none';
        option.classList.add('opacity-60');
    });

    // Mark the selected answer
    if (isCorrect) {
        element.classList.add('border-green-500', 'bg-green-100');
        element.innerHTML += ' <i class="fas fa-check text-green-600 float-left"></i>';
        quizScore++;
    } else {
        element.classList.add('border-red-500', 'bg-red-100');
        element.innerHTML += ' <i class="fas fa-times text-red-600 float-left"></i>';
        
        // Show correct answer
        allOptions.forEach(option => {
            if (option.onclick && option.onclick.toString().includes('true')) {
                option.classList.add('border-green-500', 'bg-green-100');
                option.innerHTML += ' <i class="fas fa-check text-green-600 float-left"></i>';
            }
        });
    }

    answeredQuestions++;
    
    // Show results if all questions answered
    if (answeredQuestions >= totalQuestions) {
        setTimeout(() => {
            showQuizResults();
        }, 1000);
    }
}

function showQuizResults() {
    const resultsDiv = document.getElementById('quiz-results');
    const scoreSpan = document.getElementById('quiz-score');
    
    scoreSpan.textContent = convertToWesternDigits(quizScore.toString());
    resultsDiv.classList.remove('hidden');
    resultsDiv.scrollIntoView({ behavior: 'smooth' });
    
    // Mark module as complete if score is good
    if (quizScore >= 1) {
        markModuleComplete();
    }
}

function markModuleComplete() {
    let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
    progress.fire = true;
    localStorage.setItem('courseProgress', JSON.stringify(progress));
}

function completeModule() {
    markModuleComplete();
    showNotification('تم إكمال وحدة الحماية من الحرائق بنجاح!', 'success');
    setTimeout(() => {
        window.location.href = '../index.html';
    }, 1500);
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`;
    
    if (type === 'success') {
        notification.classList.add('bg-green-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-check-circle ml-2"></i>${message}`;
    } else if (type === 'error') {
        notification.classList.add('bg-red-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-exclamation-circle ml-2"></i>${message}`;
    } else {
        notification.classList.add('bg-blue-500', 'text-white');
        notification.innerHTML = `<i class="fas fa-info-circle ml-2"></i>${message}`;
    }
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Animate out
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add welcome message
    setTimeout(() => {
        addAlert('مرحباً بك في محاكي نظام الحماية من الحرائق', 'info');
    }, 1000);
});

// PASS Technique Simulator
let passState = {
    pull: false,
    aim: false,
    squeeze: false,
    sweep: false
};
let fireStarted = false;

function startSimulation() {
    if (fireStarted) return;
    fireStarted = true;

    document.getElementById('fire-flames').classList.remove('hidden');
    document.getElementById('start-simulation').disabled = true;
    document.getElementById('start-simulation').classList.add('opacity-50');
    document.getElementById('progress-text').textContent = 'اسحب مسمار الأمان لبدء الإطفاء.';
    
    updateProgress();
    enableStep('pull');
}

function executeStep(step) {
    if (!fireStarted) return;

    switch(step) {
        case 'pull':
            passState.pull = true;
            updateProgress();
            document.getElementById('progress-text').textContent = 'جيد! الآن وجه الطفاية نحو قاعدة النار.';
            disableStep('pull');
            enableStep('aim');
            break;
        case 'aim':
            if (passState.pull) {
                passState.aim = true;
                updateProgress();
                document.getElementById('progress-text').textContent = 'ممتاز! الآن اضغط على المقبض.';
                disableStep('aim');
                enableStep('squeeze');
            }
            break;
        case 'squeeze':
            if (passState.aim) {
                passState.squeeze = true;
                document.getElementById('extinguisher-spray').classList.remove('hidden');
                updateProgress();
                document.getElementById('progress-text').textContent = 'استمر! حرك الطفاية يميناً ويساراً.';
                disableStep('squeeze');
                enableStep('sweep');
            }
            break;
        case 'sweep':
            if (passState.squeeze) {
                passState.sweep = true;
                updateProgress();
                document.getElementById('progress-text').textContent = 'أحسنت! لقد تم إطفاء الحريق بنجاح.';
                extinguishFire();
                disableStep('sweep');
            }
            break;
    }
}

function resetSimulation() {
    fireStarted = false;
    passState = { pull: false, aim: false, squeeze: false, sweep: false };

    document.getElementById('fire-flames').classList.add('hidden');
    document.getElementById('extinguisher-spray').classList.add('hidden');
    document.getElementById('success-message').classList.add('hidden');
    document.getElementById('start-simulation').disabled = false;
    document.getElementById('start-simulation').classList.remove('opacity-50');

    updateProgress();
    document.getElementById('progress-text').textContent = 'ابدأ التدريب بالنقر على "إشعال النار"';

    disableStep('pull');
    disableStep('aim');
    disableStep('squeeze');
    disableStep('sweep');
}

function updateProgress() {
    let completedSteps = Object.values(passState).filter(Boolean).length;
    let progress = (completedSteps / 4) * 100;
    document.getElementById('training-progress').style.width = `${convertToWesternDigits(progress.toString())}%`;
}

function enableStep(step) {
    const btn = document.getElementById(`${step}-btn`);
    btn.disabled = false;
    btn.parentElement.classList.add('animate-pulse');
}

function disableStep(step) {
    const btn = document.getElementById(`${step}-btn`);
    btn.disabled = true;
    btn.parentElement.classList.remove('animate-pulse');
}

function extinguishFire() {
    document.getElementById('fire-flames').classList.add('hidden');
    document.getElementById('extinguisher-spray').classList.add('hidden');
    document.getElementById('success-message').classList.remove('hidden');
}

function showFireInfo(type) {
    const infoPanel = document.getElementById('fire-info-panel');
    const title = document.getElementById('fire-info-title');
    const content = document.getElementById('fire-info-content');

    const fireData = {
        'A': { title: 'حرائق الفئة A', content: 'هي حرائق المواد الصلبة القابلة للاشتعال مثل الخشب والورق. أفضل طريقة لإطفائها هي استخدام الماء أو طفايات البودرة الكيميائية الجافة.' },
        'B': { title: 'حرائق الفئة B', content: 'هي حرائق السوائل القابلة للاشتعال مثل الزيوت والبنزين. يجب استخدام طفايات الرغوة، ثاني أكسيد الكربون، أو البودرة الجافة لإطفائها. لا تستخدم الماء.' },
        'C': { title: 'حرائق الفئة C', content: 'هي حرائق المعدات الكهربائية. يجب استخدام طفايات ثاني أكسيد الكربون أو البودرة الجافة. من الخطر استخدام الماء لأنها موصلة للكهرباء.' },
        'D': { title: 'حرائق الفئة D', content: 'هي حرائق المعادن القابلة للاشتعال مثل الماغنسيوم. تتطلب طفايات خاصة جداً تحتوي على بودرة جافة مخصصة لهذه المعادن.' }
    };

    title.textContent = fireData[type].title;
    content.textContent = fireData[type].content;
    infoPanel.classList.remove('hidden');
    infoPanel.scrollIntoView({ behavior: 'smooth' });
}

function selectFireAnswer(element, isCorrect) {
    const questionDiv = element.closest('.quiz-question');
    const allOptions = questionDiv.querySelectorAll('.quiz-option');
    
    allOptions.forEach(option => {
        option.style.pointerEvents = 'none';
        option.classList.add('opacity-60');
    });

    if (isCorrect) {
        element.classList.add('border-green-500', 'bg-green-100');
        quizScore++;
    } else {
        element.classList.add('border-red-500', 'bg-red-100');
    }
    
    answeredQuestions++;
    if (answeredQuestions >= 3) {
        showFireQuizResults();
    }
}

function showFireQuizResults() {
    document.getElementById('fire-quiz-score').textContent = convertToWesternDigits(quizScore.toString());
    document.getElementById('fire-quiz-results').classList.remove('hidden');
}
