# IT Collaborator 2025 - Team Setup Script
# This script sets up the application for team access

Write-Host "=== IT Collaborator 2025 - Team Setup ===" -ForegroundColor Green
Write-Host ""

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✓ Python found: $pythonVersion" -ForegroundColor Green
}
catch {
    Write-Host "✗ Python not found. Please install Python 3.7+ from https://python.org" -ForegroundColor Red
    exit 1
}

# Check if Node.js is installed (for desktop app option)
try {
    $nodeVersion = node --version 2>&1
    Write-Host "✓ Node.js found: $nodeVersion" -ForegroundColor Green
}
catch {
    Write-Host "! Node.js not found. Install from https://nodejs.org for desktop app option" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Installing Python dependencies..." -ForegroundColor Blue
pip install -r requirements.txt

# Get the computer's IP address
$ipAddress = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "Wi-Fi*" | Where-Object {$_.IPAddress -ne "127.0.0.1"}).IPAddress
if (-not $ipAddress) {
    $ipAddress = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "Ethernet*" | Where-Object {$_.IPAddress -ne "127.0.0.1"}).IPAddress
}

Write-Host ""
Write-Host "=== TEAM ACCESS INSTRUCTIONS ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. RUN THE APPLICATION:" -ForegroundColor Yellow
Write-Host "   Execute: python app.py" -ForegroundColor White
Write-Host ""
Write-Host "2. TEAM ACCESS:" -ForegroundColor Yellow
Write-Host "   Local access: http://127.0.0.1:5000" -ForegroundColor White
if ($ipAddress) {
    Write-Host "   Network access: http://$ipAddress:5000" -ForegroundColor White
    Write-Host "   (Share this URL with your team members)" -ForegroundColor Green
}
Write-Host ""
Write-Host "3. FIREWALL SETUP:" -ForegroundColor Yellow
Write-Host "   - Make sure Windows Firewall allows Python/Flask on port 5000" -ForegroundColor White
Write-Host "   - Run this as administrator if needed:" -ForegroundColor White
Write-Host "     netsh advfirewall firewall add rule name='IT Collaborator 2025' dir=in action=allow protocol=TCP localport=5000" -ForegroundColor Gray
Write-Host ""
Write-Host "4. DESKTOP APP OPTION:" -ForegroundColor Yellow
Write-Host "   - Run 'npm install' then 'npm start' for desktop version" -ForegroundColor White
Write-Host "   - Or use: install-and-run.bat" -ForegroundColor White
Write-Host ""
Write-Host "=== READY TO START ===" -ForegroundColor Green
Write-Host "Run: python app.py" -ForegroundColor White
Write-Host ""
