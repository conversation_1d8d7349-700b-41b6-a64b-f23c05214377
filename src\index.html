<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IT Collaborator 2025 - الشبكات وأمن المعلومات</title>
    <link rel="icon" type="image/png" href="/assets/images/techno.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .module-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .module-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            border: none;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .logo {
            position: absolute;
            left: -3px;
            top: 0px;
            width: 800px;
            z-index: 50;
        }

        .progress-bar {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            height: 10px;
            border-radius: 5px;
            transition: width 0.8s ease;
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <span class="text-2xl font-bold text-gray-800">IT Collaborator 2025</span>
                </div>
                <img src="/assets/images/techno1.png" alt="Logo" class="logo mx-auto" />
                <div style="margin-top: 20px; z-index: 55;" class="hidden md:flex space-x-reverse space-x-8">
                    <a href="#home" class="text-gray-700 hover:text-blue-600 transition-colors"><b>الرئيسية</b></a>
                    <a href="#modules" class="text-gray-700 hover:text-blue-600 transition-colors"><b>الوحدات</b></a>
                    <a href="#progress" class="text-gray-700 hover:text-blue-600 transition-colors"><b>التقدم</b></a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section relative">
        <div
            class="absolute inset-0 top-16 md:top-0 flex flex-col items-center justify-center text-center text-white px-4">
            <h1 class="text-4xl md:text-6xl font-bold mb-4">IT Collaborator 2025</h1>
            <h2 class="text-xl md:text-3xl font-semibold mb-6">الشبكات وأمن المعلومات</h2>
            <p class="text-base md:text-xl mb-8 max-w-3xl mx-auto">
                رحلة شاملة نحو إتقان أساسيات الشبكات، أمن المعلومات، وأنظمة الحماية الحديثة
            </p>
            <button onclick="startCourse()" class="btn-primary text-base md:text-lg px-6 py-3">
                <i class="fas fa-play ml-2"></i>
                ابدأ التعلم الآن
            </button>
        </div>
    </section>


    <!-- Progress Section -->
    <section id="progress" class="py-16 bg-white">
        <div class="max-w-6xl mx-auto px-4">
            <h2 class="text-4xl font-bold text-center mb-12 text-gray-800">تقدمك في الدورة</h2>
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 mb-8">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <span class="text-2xl font-bold text-gray-800">التقدم الإجمالي</span>
                        <p class="text-gray-600">مبني على 6 وحدات أساسية</p>
                    </div>
                    <span class="text-3xl font-bold text-blue-600" id="overall-progress">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-4">
                    <div class="progress-bar h-4 rounded-full" style="width: 0%" id="progress-bar"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Modules Section -->
    <section id="modules" class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4">
            <h2 class="text-4xl font-bold text-center mb-12 text-gray-800">وحدات الدورة</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">

                <!-- Module 1: Introduction -->
                <div class="module-card" data-module="introduction">
                    <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
                        <i class="fas fa-rocket text-5xl mb-4"></i>
                        <h3 class="text-2xl font-bold">مقدمة تحفيزية</h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">لماذا هذه المهارات مهمة؟</p>
                        <div class="flex items-center justify-between">
                            <span class="module-progress-text text-sm text-gray-500">0% مكتمل</span>
                            <button onclick="openModule('introduction')" class="btn-primary">
                                <i class="fas fa-play mr-2"></i>
                                ابدأ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Module 2: Networks -->
                <div class="module-card" data-module="networks">
                    <div class="bg-gradient-to-r from-green-500 to-teal-600 p-6 text-white">
                        <i class="fas fa-network-wired text-5xl mb-4"></i>
                        <h3 class="text-2xl font-bold">أساسيات الشبكات</h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">تعلم أساسيات الشبكات والاتصالات</p>
                        <div class="flex items-center justify-between">
                            <span class="module-progress-text text-sm text-gray-500">0% مكتمل</span>
                            <button onclick="openModule('networks')" class="btn-primary">
                                <i class="fas fa-play mr-2"></i>
                                ابدأ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Module 3: Security -->
                <div class="module-card" data-module="security">
                    <div class="bg-gradient-to-r from-red-500 to-pink-600 p-6 text-white">
                        <i class="fas fa-shield-alt text-5xl mb-4"></i>
                        <h3 class="text-2xl font-bold">أمن المعلومات</h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">حماية البيانات والأنظمة</p>
                        <div class="flex items-center justify-between">
                            <span class="module-progress-text text-sm text-gray-500">0% مكتمل</span>
                            <button onclick="openModule('security')" class="btn-primary">
                                <i class="fas fa-play mr-2"></i>
                                ابدأ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Module 4: Alarm Systems -->
                <div class="module-card" data-module="alarm">
                    <div class="bg-gradient-to-r from-yellow-500 to-orange-600 p-6 text-white">
                        <i class="fas fa-exclamation-triangle text-5xl mb-4"></i>
                        <h3 class="text-2xl font-bold">أنظمة الإنذار</h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">اكتشاف التهديدات مبكراً</p>
                        <div class="flex items-center justify-between">
                            <span class="module-progress-text text-sm text-gray-500">0% مكتمل</span>
                            <button onclick="openModule('alarm')" class="btn-primary">
                                <i class="fas fa-play mr-2"></i>
                                ابدأ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Module 5: Fire Protection -->
                <div class="module-card" data-module="fire">
                    <div class="bg-gradient-to-r from-red-600 to-red-800 p-6 text-white">
                        <i class="fas fa-fire-extinguisher text-5xl mb-4"></i>
                        <h3 class="text-2xl font-bold">الحماية من الحرائق</h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">أنظمة مكافحة الحرائق</p>
                        <div class="flex items-center justify-between">
                            <span class="module-progress-text text-sm text-gray-500">0% مكتمل</span>
                            <button onclick="openModule('fire')" class="btn-primary">
                                <i class="fas fa-play mr-2"></i>
                                ابدأ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Module 6: Video Surveillance -->
                <div class="module-card" data-module="video">
                    <div class="bg-gradient-to-r from-purple-500 to-indigo-600 p-6 text-white">
                        <i class="fas fa-video text-5xl mb-4"></i>
                        <h3 class="text-2xl font-bold">المراقبة بالفيديو</h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">أنظمة المراقبة والحماية</p>
                        <div class="flex items-center justify-between">
                            <span class="module-progress-text text-sm text-gray-500">0% مكتمل</span>
                            <button onclick="openModule('video')" class="btn-primary">
                                <i class="fas fa-play mr-2"></i>
                                ابدأ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Module 7: Fire Protection Systems -->
                <div class="module-card" data-module="fire-protection">
                    <div class="bg-gradient-to-r from-blue-500 to-indigo-600 p-6 text-white">
                        <i class="fas fa-shield-alt text-5xl mb-4"></i>
                        <h3 class="text-2xl font-bold">أنظمة الحماية من الحرائق</h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">مكونات وأنواع أنظمة الإنذار</p>
                        <div class="flex items-center justify-between">
                            <span class="module-progress-text text-sm text-gray-500">0% مكتمل</span>
                            <button onclick="openModule('fire-protection')" class="btn-primary">
                                <i class="fas fa-play mr-2"></i>
                                ابدأ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Module 8: Sophos Firewalls -->
                <div class="module-card" data-module="sophos-firewalls">
                    <div class="bg-gradient-to-r from-cyan-500 to-blue-600 p-6 text-white">
                        <i class="fas fa-shield-alt text-5xl mb-4"></i>
                        <h3 class="text-2xl font-bold">جدران حماية Sophos</h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">أنظمة الحماية المتقدمة</p>
                        <div class="flex items-center justify-between">
                            <span class="module-progress-text text-sm text-gray-500">0% مكتمل</span>
                            <button onclick="openModule('sophos-firewalls')" class="btn-primary">
                                <i class="fas fa-play mr-2"></i>
                                ابدأ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="max-w-7xl mx-auto px-4 text-center">
            <p>&copy; DSI_IT Collaborator 2025 - Kamel Balla</p>
        </div>
    </footer>

    <script>
        // Open specific module
        function openModule(moduleId) {
            window.location.href = `pages/${moduleId}.html`;
        }

        // Start the course
        function startCourse() {
            document.getElementById('modules').scrollIntoView({ behavior: 'smooth' });
        }

        // Progress tracking
        let progress = JSON.parse(localStorage.getItem('courseProgress')) || {};

        function updateProgress() {
            const coreModules = ['networks', 'security', 'alarm', 'fire', 'video', 'fire-protection'];
            const completedModules = coreModules.filter(module => progress[module]).length;
            const percentage = Math.round((completedModules / coreModules.length) * 100);

            document.getElementById('progress-bar').style.width = percentage + '%';
            document.getElementById('overall-progress').textContent = percentage + '%';

        }

        // Initialize
        updateProgress();

        // Update progress when page becomes visible
        document.addEventListener('visibilitychange', function () {
            if (!document.hidden) {
                progress = JSON.parse(localStorage.getItem('courseProgress')) || {};
                updateProgress();
            }
        });
    </script>
</body>

</html>